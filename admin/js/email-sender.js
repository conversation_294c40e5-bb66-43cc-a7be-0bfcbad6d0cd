/**
 * Email Sender Module
 * 
 * Handles sending form submissions from the dashboard via the PHP backend.
 * Includes security measures like CSRF protection and validation.
 */

document.addEventListener('DOMContentLoaded', function() {
  // Initialize CSRF token
  let csrfToken = '';
  
  // Get CSRF token from meta tag (will be added to dashboard.html)
  const csrfMeta = document.querySelector('meta[name="csrf-token"]');
  if (csrfMeta) {
    csrfToken = csrfMeta.getAttribute('content');
  }
  
  // Add email sending functionality to the dashboard
  initializeEmailSender();
  
  /**
   * Initialize email sender functionality
   */
  function initializeEmailSender() {
    // Add email buttons to meeting requests and contact forms tables
    addEmailButtonsToTables();
    
    // Create email modal
    createEmailModal();
  }
  
  /**
   * Add email buttons to tables
   */
  function addEmailButtonsToTables() {
    // Add to meeting requests table headers
    const meetingTableHeader = document.querySelector('#meeting-requests table thead tr');
    if (meetingTableHeader) {
      const lastHeaderCell = meetingTableHeader.querySelector('th:last-child');
      lastHeaderCell.textContent = 'Aktionen';
    }
    
    // Add to contact forms table headers
    const contactTableHeader = document.querySelector('#contact-forms table thead tr');
    if (contactTableHeader) {
      const lastHeaderCell = contactTableHeader.querySelector('th:last-child');
      lastHeaderCell.textContent = 'Aktionen';
    }
    
    // Add email buttons to existing rows
    addEmailButtonsToExistingRows();
  }
  
  /**
   * Add email buttons to existing table rows
   */
  function addEmailButtonsToExistingRows() {
    // Add to meeting requests rows
    const meetingRows = document.querySelectorAll('#meeting-table-body tr');
    meetingRows.forEach(row => {
      const actionsCell = row.querySelector('td:last-child');
      if (actionsCell) {
        const emailBtn = document.createElement('button');
        emailBtn.className = 'action-btn email';
        emailBtn.setAttribute('data-id', actionsCell.querySelector('.view').getAttribute('data-id'));
        emailBtn.setAttribute('data-type', 'meeting');
        emailBtn.setAttribute('title', 'Per E-Mail senden');
        emailBtn.innerHTML = '<i class="fas fa-envelope"></i>';
        
        // Insert before delete button
        const deleteBtn = actionsCell.querySelector('.delete');
        actionsCell.insertBefore(emailBtn, deleteBtn);
        
        // Add event listener
        emailBtn.addEventListener('click', handleEmailButtonClick);
      }
    });
    
    // Add to contact forms rows
    const contactRows = document.querySelectorAll('#contact-table-body tr');
    contactRows.forEach(row => {
      const actionsCell = row.querySelector('td:last-child');
      if (actionsCell) {
        const emailBtn = document.createElement('button');
        emailBtn.className = 'action-btn email';
        emailBtn.setAttribute('data-id', actionsCell.querySelector('.view').getAttribute('data-id'));
        emailBtn.setAttribute('data-type', 'contact');
        emailBtn.setAttribute('title', 'Per E-Mail senden');
        emailBtn.innerHTML = '<i class="fas fa-envelope"></i>';
        
        // Insert before delete button
        const deleteBtn = actionsCell.querySelector('.delete');
        actionsCell.insertBefore(emailBtn, deleteBtn);
        
        // Add event listener
        emailBtn.addEventListener('click', handleEmailButtonClick);
      }
    });
  }
  
  /**
   * Create email modal
   */
  function createEmailModal() {
    const modalHtml = `
      <div class="modal" id="email-modal">
        <div class="modal-content">
          <div class="modal-header">
            <h3 id="email-modal-title">Anfrage per E-Mail senden</h3>
            <button class="close-modal">&times;</button>
          </div>
          <div class="modal-body">
            <form id="email-form">
              <input type="hidden" id="submission-type" name="submission_type" value="">
              <input type="hidden" id="submission-data" name="submission_data" value="">
              <input type="hidden" id="csrf-token" name="csrf_token" value="${csrfToken}">
              
              <!-- Honeypot field (invisible to humans) -->
              <div style="display:none;">
                <input type="text" name="website" id="website" autocomplete="off">
              </div>
              
              <div class="form-group">
                <label for="recipient-email">Empfänger E-Mail</label>
                <input type="email" id="recipient-email" name="recipient_email" required>
              </div>
              
              <div class="form-group">
                <label for="additional-message">Zusätzliche Nachricht (optional)</label>
                <textarea id="additional-message" name="additional_message" rows="4"></textarea>
              </div>
            </form>
            
            <div id="email-status" class="status-message" style="display:none;"></div>
          </div>
          <div class="modal-footer">
            <button class="btn btn-outline close-modal">Abbrechen</button>
            <button class="btn btn-primary" id="send-email-btn">E-Mail senden</button>
          </div>
        </div>
      </div>
    `;
    
    // Append modal to body
    document.body.insertAdjacentHTML('beforeend', modalHtml);
    
    // Add event listeners
    const emailModal = document.getElementById('email-modal');
    const closeButtons = emailModal.querySelectorAll('.close-modal');
    const sendEmailBtn = document.getElementById('send-email-btn');
    
    closeButtons.forEach(button => {
      button.addEventListener('click', () => {
        emailModal.style.display = 'none';
      });
    });
    
    // Close modal when clicking outside
    window.addEventListener('click', (e) => {
      if (e.target === emailModal) {
        emailModal.style.display = 'none';
      }
    });
    
    // Send email button
    sendEmailBtn.addEventListener('click', sendEmail);
  }
  
  /**
   * Handle email button click
   */
  function handleEmailButtonClick(e) {
    const button = e.currentTarget;
    const type = button.getAttribute('data-type');
    const id = button.getAttribute('data-id');
    
    // Get submission data
    let submissionData = null;
    
    if (type === 'meeting') {
      const meetingRequests = JSON.parse(localStorage.getItem('meetingRequests')) || [];
      submissionData = meetingRequests[id];
    } else if (type === 'contact') {
      const contactForms = JSON.parse(localStorage.getItem('contactForms')) || [];
      submissionData = contactForms[id];
    }
    
    if (!submissionData) {
      alert('Fehler: Anfrage konnte nicht gefunden werden.');
      return;
    }
    
    // Populate modal
    const emailModal = document.getElementById('email-modal');
    const submissionTypeInput = document.getElementById('submission-type');
    const submissionDataInput = document.getElementById('submission-data');
    const recipientEmailInput = document.getElementById('recipient-email');
    const additionalMessageInput = document.getElementById('additional-message');
    const emailStatus = document.getElementById('email-status');
    
    // Set default values
    submissionTypeInput.value = type;
    submissionDataInput.value = JSON.stringify(submissionData);
    recipientEmailInput.value = submissionData.email || '';
    additionalMessageInput.value = '';
    
    // Reset status message
    emailStatus.style.display = 'none';
    emailStatus.textContent = '';
    emailStatus.className = 'status-message';
    
    // Show modal
    emailModal.style.display = 'block';
  }
  
  /**
   * Send email via PHP backend
   */
  function sendEmail() {
    const emailForm = document.getElementById('email-form');
    const emailStatus = document.getElementById('email-status');
    const sendEmailBtn = document.getElementById('send-email-btn');
    
    // Basic validation
    const recipientEmail = document.getElementById('recipient-email').value;
    if (!recipientEmail || !validateEmail(recipientEmail)) {
      showEmailStatus('Bitte geben Sie eine gültige E-Mail-Adresse ein.', 'error');
      return;
    }
    
    // Disable button and show loading state
    sendEmailBtn.disabled = true;
    sendEmailBtn.textContent = 'Wird gesendet...';
    
    // Prepare form data
    const formData = new FormData(emailForm);
    
    // Send request
    fetch('send_submissions.php', {
      method: 'POST',
      body: formData
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        showEmailStatus('E-Mail wurde erfolgreich gesendet!', 'success');
        
        // Close modal after 2 seconds
        setTimeout(() => {
          document.getElementById('email-modal').style.display = 'none';
        }, 2000);
      } else {
        showEmailStatus('Fehler: ' + data.message, 'error');
      }
    })
    .catch(error => {
      showEmailStatus('Ein Fehler ist aufgetreten: ' + error.message, 'error');
      console.error('Error:', error);
    })
    .finally(() => {
      // Reset button
      sendEmailBtn.disabled = false;
      sendEmailBtn.textContent = 'E-Mail senden';
    });
  }
  
  /**
   * Show email status message
   */
  function showEmailStatus(message, type) {
    const emailStatus = document.getElementById('email-status');
    emailStatus.textContent = message;
    emailStatus.className = 'status-message ' + type;
    emailStatus.style.display = 'block';
  }
  
  /**
   * Validate email format
   */
  function validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
  }
  
  // Override the original loadMeetingRequests and loadContactForms functions
  // to add email buttons to new rows
  const originalLoadMeetingRequests = window.loadMeetingRequests;
  window.loadMeetingRequests = function() {
    originalLoadMeetingRequests.apply(this, arguments);
    addEmailButtonsToExistingRows();
  };
  
  const originalLoadContactForms = window.loadContactForms;
  window.loadContactForms = function() {
    originalLoadContactForms.apply(this, arguments);
    addEmailButtonsToExistingRows();
  };
});
