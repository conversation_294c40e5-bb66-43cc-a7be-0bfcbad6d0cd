# Heart & Soul Medienproduktion - Admin Dashboard

## Form Submission Email System

This system allows administrators to send form submissions from the dashboard via email. The implementation includes multiple security measures to prevent common vulnerabilities.

### Features

- Send meeting requests and contact form submissions via email
- Add custom messages to the emails
- Secure implementation with multiple security measures
- User-friendly interface integrated into the dashboard

### Security Measures Implemented

| Maßnahme | Beschreibung | Implementierung |
|----------|--------------|-----------------|
| Validierung/Säuberung | Immer htmlspecialchars() und trim() nutzen | Alle Eingaben werden mit `sanitize_input()` bereinigt, die beide Funktionen verwendet |
| CSRF-Schutz | Z.B. mit verstecktem Token `<input type="hidden" name="csrf_token">` | CSRF-Token wird in der Session gespeichert und mit jedem Formular gesendet |
| Honeypot-Feld | Ein unsichtbares Feld, das Bots füllen, echte <PERSON>utzer aber nicht | Verstecktes Feld "website" wird im Formular eingefügt und auf Leerheit geprüft |
| HTTPS verwenden | Transportverschlüsselung gegen Abhören | Empfohlen für die Produktionsumgebung |
| Header-Injection verhindern | mail()-Felder auf Zeilenumbrüche prüfen | Funktion `has_header_injection()` prüft alle Header-Felder auf Zeilenumbrüche |
| Content Security Policy | Schutz vor XSS-Angriffen | CSP-Header werden sowohl per HTTP als auch per Meta-Tag gesetzt |
| X-Content-Type-Options | Verhindert MIME-Sniffing | Header wird gesetzt auf "nosniff" |
| X-Frame-Options | Schutz vor Clickjacking | Header wird gesetzt auf "DENY" |
| X-XSS-Protection | Zusätzlicher XSS-Schutz | Header wird gesetzt auf "1; mode=block" |

### Files

- `send_submissions.php` - Backend-Script zum Senden der E-Mails
- `js/email-sender.js` - Frontend-Script für die E-Mail-Funktionalität
- `dashboard.php` - Dashboard mit integrierter E-Mail-Funktionalität

### Usage

1. Log in to the admin dashboard
2. Navigate to either "Terminanfragen" or "Kontaktanfragen"
3. Click the envelope icon next to any entry
4. Enter the recipient email address (pre-filled with the submitter's email if available)
5. Optionally add a custom message
6. Click "E-Mail senden"

### Technical Implementation

#### Backend (PHP)

The PHP backend (`send_submissions.php`) handles the actual email sending with multiple security measures:

```php
// Example of sanitization
function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

// Example of header injection prevention
function has_header_injection($str) {
    return preg_match('/[\r\n]/', $str) !== 0;
}

// Example of CSRF protection
function verify_csrf_token($token) {
    if (!isset($_SESSION['csrf_token']) || empty($token)) {
        return false;
    }
    
    return hash_equals($_SESSION['csrf_token'], $token);
}
```

#### Frontend (JavaScript)

The JavaScript (`js/email-sender.js`) handles the user interface and form submission:

```javascript
// Example of sending email
function sendEmail() {
    // Prepare form data
    const formData = new FormData(emailForm);
    
    // Send request
    fetch('send_submissions.php', {
      method: 'POST',
      body: formData
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        showEmailStatus('E-Mail wurde erfolgreich gesendet!', 'success');
      } else {
        showEmailStatus('Fehler: ' + data.message, 'error');
      }
    });
}
```

### Maintenance and Updates

- Regularly check for security updates in PHP
- Consider implementing additional security measures like rate limiting
- Monitor logs for any suspicious activity
- Keep CSRF tokens refreshed for each session

### Troubleshooting

If emails are not being sent:

1. Check PHP error logs
2. Verify that the mail() function is properly configured on the server
3. Ensure all required fields are being submitted
4. Check for any CSRF token mismatches
5. Verify that the honeypot field is not being filled
