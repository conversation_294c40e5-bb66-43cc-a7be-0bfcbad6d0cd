<?php
/**
 * Form Submission Email Sender
 * 
 * This script securely processes and sends form submissions from the admin dashboard.
 * It includes multiple security measures to prevent common vulnerabilities.
 */

// Start session for CSRF protection
session_start();

// Secure session cookie attributes
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', 1); // Ensure this is 1 if your site is HTTPS, 0 for HTTP development
ini_set('session.use_only_cookies', 1);
ini_set('session.cookie_samesite', 'Strict'); // Or 'Lax'

// Set strict error reporting
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', 'php_errors.log');

// Configuration
$admin_email = '<EMAIL>';
$site_name = 'Heart & Soul Medienproduktion';
$max_email_length = 1000000; // Prevent extremely large emails

// Security headers
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');
header('Content-Security-Policy: default-src \'self\'');

/**
 * Sanitize and validate input
 * 
 * @param string $data Input data to sanitize
 * @return string Sanitized data
 */
function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

/**
 * Validate email address
 * 
 * @param string $email Email to validate
 * @return bool True if valid, false otherwise
 */
function is_valid_email($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Check for header injection attempts
 * 
 * @param string $str String to check
 * @return bool True if injection detected, false otherwise
 */
function has_header_injection($str) {
    return preg_match('/[\r\n]/', $str) !== 0;
}

/**
 * Generate CSRF token
 * 
 * @return string CSRF token
 */
function generate_csrf_token() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * Verify CSRF token
 * 
 * @param string $token Token to verify
 * @return bool True if valid, false otherwise
 */
function verify_csrf_token($token) {
    if (!isset($_SESSION['csrf_token']) || empty($token)) {
        return false;
    }
    
    return hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Check honeypot field
 * 
 * @return bool True if honeypot is empty (human), false if filled (bot)
 */
function check_honeypot() {
    return !isset($_POST['website']) || $_POST['website'] === '';
}

/**
 * Send email securely
 * 
 * @param string $to Recipient email
 * @param string $subject Email subject
 * @param string $message Email message
 * @param string $from_email Sender email
 * @param string $from_name Sender name
 * @return bool True if sent successfully, false otherwise
 */
function send_secure_email($to, $subject, $message, $from_email, $from_name) {
    // Validate inputs to prevent header injection
    if (has_header_injection($to) || has_header_injection($subject) || 
        has_header_injection($from_email) || has_header_injection($from_name)) {
        error_log('Header injection attempt detected');
        return false;
    }
    
    // Validate email addresses
    if (!is_valid_email($to) || !is_valid_email($from_email)) {
        error_log('Invalid email address detected');
        return false;
    }
    
    // Prepare headers
    $headers = "From: " . $from_name . " <" . $from_email . ">\r\n";
    $headers .= "Reply-To: " . $from_email . "\r\n";
    $headers .= "MIME-Version: 1.0\r\n";
    $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
    $headers .= "X-Mailer: PHP/" . phpversion();
    
    // Limit message size
    if (strlen($message) > $GLOBALS['max_email_length']) {
        $message = substr($message, 0, $GLOBALS['max_email_length']) . '... [Content truncated due to size]';
    }
    
    // Send email
    return mail($to, $subject, $message, $headers);
}

// Initialize response array
$response = [
    'success' => false,
    'message' => 'An error occurred'
];

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    
    // Verify CSRF token
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $response['message'] = 'Security token validation failed';
        error_log('CSRF token validation failed');
        echo json_encode($response);
        exit;
    }
    
    // Check honeypot
    if (!check_honeypot()) {
        // Don't reveal that we detected a bot
        $response['success'] = true;
        $response['message'] = 'Form processed successfully';
        echo json_encode($response);
        exit;
    }
    
    // Validate required fields
    $required_fields = ['submission_type', 'submission_data', 'recipient_email'];
    foreach ($required_fields as $field) {
        if (!isset($_POST[$field]) || empty($_POST[$field])) {
            $response['message'] = 'Missing required fields';
            echo json_encode($response);
            exit;
        }
    }
    
    // Sanitize inputs
    $submission_type = sanitize_input($_POST['submission_type']);
    $submission_data = json_decode($_POST['submission_data'], true);
    $recipient_email = sanitize_input($_POST['recipient_email']);
    $additional_message = isset($_POST['additional_message']) ? sanitize_input($_POST['additional_message']) : '';
    
    // Validate recipient email
    if (!is_valid_email($recipient_email)) {
        $response['message'] = 'Invalid recipient email address';
        echo json_encode($response);
        exit;
    }
    
    // Validate submission data
    if (!is_array($submission_data)) {
        $response['message'] = 'Invalid submission data format';
        echo json_encode($response);
        exit;
    }
    
    // Sanitize all submission data fields
    foreach ($submission_data as $key => $value) {
        if (is_string($value)) {
            $submission_data[$key] = sanitize_input($value);
        }
    }
    
    // Prepare email content based on submission type
    $subject = '';
    $message = '';
    
    if ($submission_type === 'meeting') {
        $subject = 'Terminanfrage von ' . ($submission_data['name'] ?? 'Unbekannt');
        
        // Format date if available
        $meeting_date = '';
        if (isset($submission_data['date']) && isset($submission_data['time'])) {
            $meeting_date = $submission_data['date'] . ' um ' . $submission_data['time'];
        }
        
        // Build HTML message
        $message = '
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>' . $subject . '</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="background-color: #f5f5f5; padding: 20px; border-radius: 5px; border-left: 4px solid #ff3030;">
                <h2 style="color: #ff3030; margin-top: 0;">Neue Terminanfrage</h2>
                <p>Eine neue Terminanfrage wurde über das Dashboard weitergeleitet.</p>
            </div>
            
            <div style="margin-top: 30px;">
                <h3>Kontaktdaten:</h3>
                <ul style="list-style-type: none; padding-left: 0;">
                    <li><strong>Name:</strong> ' . ($submission_data['name'] ?? 'Nicht angegeben') . '</li>
                    <li><strong>Firma:</strong> ' . ($submission_data['company'] ?? 'Nicht angegeben') . '</li>
                    <li><strong>E-Mail:</strong> ' . ($submission_data['email'] ?? 'Nicht angegeben') . '</li>
                    <li><strong>Telefon:</strong> ' . ($submission_data['phone'] ?? 'Nicht angegeben') . '</li>
                </ul>
            </div>
            
            <div style="margin-top: 30px;">
                <h3>Termindetails:</h3>
                <ul style="list-style-type: none; padding-left: 0;">
                    <li><strong>Gewünschter Termin:</strong> ' . $meeting_date . '</li>
                    <li><strong>Eingegangen am:</strong> ' . ($submission_data['timestamp'] ?? date('Y-m-d H:i:s')) . '</li>
                </ul>
            </div>';
            
        // Add additional message if provided
        if (!empty($additional_message)) {
            $message .= '
            <div style="margin-top: 30px; padding: 15px; background-color: #f9f9f9; border-radius: 5px;">
                <h3>Zusätzliche Nachricht:</h3>
                <p>' . nl2br($additional_message) . '</p>
            </div>';
        }
        
        $message .= '
            <div style="margin-top: 30px; font-size: 12px; color: #777; border-top: 1px solid #eee; padding-top: 20px;">
                <p>Diese E-Mail wurde automatisch vom Heart & Soul Medienproduktion Admin-System gesendet.</p>
            </div>
        </body>
        </html>';
    } 
    elseif ($submission_type === 'contact') {
        $subject = 'Kontaktanfrage von ' . ($submission_data['name'] ?? 'Unbekannt');
        
        // Build HTML message
        $message = '
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>' . $subject . '</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="background-color: #f5f5f5; padding: 20px; border-radius: 5px; border-left: 4px solid #ff3030;">
                <h2 style="color: #ff3030; margin-top: 0;">Neue Kontaktanfrage</h2>
                <p>Eine neue Kontaktanfrage wurde über das Dashboard weitergeleitet.</p>
            </div>
            
            <div style="margin-top: 30px;">
                <h3>Kontaktdaten:</h3>
                <ul style="list-style-type: none; padding-left: 0;">
                    <li><strong>Name:</strong> ' . ($submission_data['name'] ?? 'Nicht angegeben') . '</li>
                    <li><strong>E-Mail:</strong> ' . ($submission_data['email'] ?? 'Nicht angegeben') . '</li>
                    ' . (isset($submission_data['phone']) ? '<li><strong>Telefon:</strong> ' . $submission_data['phone'] . '</li>' : '') . '
                </ul>
            </div>
            
            <div style="margin-top: 30px;">
                <h3>Anfrage:</h3>
                <p><strong>Betreff:</strong> ' . ($submission_data['subject'] ?? 'Kein Betreff') . '</p>
                <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin-top: 10px;">
                    <p>' . nl2br($submission_data['message'] ?? 'Keine Nachricht') . '</p>
                </div>
                <p><strong>Eingegangen am:</strong> ' . ($submission_data['timestamp'] ?? date('Y-m-d H:i:s')) . '</p>
            </div>';
            
        // Add additional message if provided
        if (!empty($additional_message)) {
            $message .= '
            <div style="margin-top: 30px; padding: 15px; background-color: #f9f9f9; border-radius: 5px;">
                <h3>Zusätzliche Nachricht:</h3>
                <p>' . nl2br($additional_message) . '</p>
            </div>';
        }
        
        $message .= '
            <div style="margin-top: 30px; font-size: 12px; color: #777; border-top: 1px solid #eee; padding-top: 20px;">
                <p>Diese E-Mail wurde automatisch vom Heart & Soul Medienproduktion Admin-System gesendet.</p>
            </div>
        </body>
        </html>';
    }
    else {
        $response['message'] = 'Invalid submission type';
        echo json_encode($response);
        exit;
    }
    
    // Send email
    $email_sent = send_secure_email(
        $recipient_email,
        $subject,
        $message,
        $admin_email,
        $site_name
    );
    
    if ($email_sent) {
        $response['success'] = true;
        $response['message'] = 'Email sent successfully';
    } else {
        $response['message'] = 'Failed to send email';
        error_log('Failed to send email to ' . $recipient_email);
    }
}

// Return JSON response
header('Content-Type: application/json');
echo json_encode($response);
