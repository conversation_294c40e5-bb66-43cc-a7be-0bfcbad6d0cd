/* Admin Styles */
:root {
  --admin-primary: #ff3030;
  --admin-secondary: #2c3e50;
  --admin-success: #2ecc71;
  --admin-warning: #f39c12;
  --admin-danger: #e74c3c;
  --admin-info: #3498db;
  --admin-light: #f5f5f5;
  --admin-dark: #121212;
  --admin-gray: #a0a0a0;
  --admin-border: rgba(255, 255, 255, 0.1);
}

/* Admin Login */
.admin-login-container {
  max-width: 500px;
}

.admin-form {
  margin-top: 2rem;
}

.admin-login-container {
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.admin-login-container:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Email Functionality */
.action-btn.email {
  background-color: var(--admin-info);
  margin-right: 5px;
}

.action-btn.email:hover {
  background-color: #3498db;
}

.status-message {
  padding: 10px 15px;
  border-radius: 4px;
  margin: 15px 0;
  font-weight: 500;
}

.status-message.success {
  background-color: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
  border-left: 3px solid #2ecc71;
}

.status-message.error {
  background-color: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
  border-left: 3px solid #e74c3c;
}

/* Password Field Styling */
.password-group {
  position: relative;
}

.password-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.password-input-container input {
  flex: 1;
  padding-right: 3rem;
}

.password-toggle {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--admin-gray);
  cursor: pointer;
  transition: color 0.3s ease;
  padding: 0.5rem;
  z-index: 2;
}

.password-toggle:hover {
  color: var(--admin-light);
}

.password-toggle:focus {
  outline: none;
  color: var(--admin-primary);
}

/* Form Input Animation */
.admin-form .form-group input {
  transition: all 0.3s ease;
  background-color: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: var(--admin-light);
  font-size: 1rem;
  padding: 1.2rem;
}

.admin-form .form-group input:focus {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(255, 48, 48, 0.15);
  border-color: var(--admin-primary);
  background-color: rgba(255, 255, 255, 0.07);
}

.input-focused label {
  color: var(--admin-primary);
  transform: translateY(-5px);
  font-size: 0.9rem;
}

.form-group label {
  transition: all 0.3s ease;
  display: block;
  margin-bottom: 0.8rem;
  font-weight: 500;
}

/* Error Message Animation */
#login-error {
  animation: shake 0.5s ease-in-out;
  background-color: rgba(231, 76, 60, 0.1);
  border-left: 3px solid var(--admin-danger);
  padding: 1rem;
  border-radius: 4px;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
  20%, 40%, 60%, 80% { transform: translateX(5px); }
}

/* Login Button Enhancement */
.admin-form .btn-primary {
  background-color: var(--admin-primary);
  border: none;
  padding: 1rem 2.5rem;
  border-radius: 50px;
  font-weight: 500;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(255, 48, 48, 0.3);
}

.admin-form .btn-primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(255, 48, 48, 0.4);
  background-color: #ff4545;
}

.admin-form .btn-primary:active {
  transform: translateY(0);
  box-shadow: 0 3px 10px rgba(255, 48, 48, 0.3);
}

.admin-form .btn-primary::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.6s ease, height 0.6s ease;
  z-index: -1;
}

.admin-form .btn-primary:hover::after {
  width: 300px;
  height: 300px;
}

/* Admin Dashboard */
.admin-section {
  padding: 8rem 0 6rem;
  min-height: calc(100vh - 80px);
  width: 100%;
}

.admin-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.admin-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 2rem;
  text-align: center;
  color: var(--admin-light);
}

/* Tabs */
.admin-tabs {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
  border-bottom: 1px solid var(--admin-border);
  padding-bottom: 1rem;
}

.tab-btn {
  background: none;
  border: none;
  color: var(--admin-gray);
  font-size: 1.1rem;
  font-weight: 500;
  padding: 0.8rem 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.tab-btn:hover {
  color: var(--admin-light);
}

.tab-btn.active {
  color: var(--admin-primary);
}

.tab-btn.active::after {
  content: '';
  position: absolute;
  bottom: -1rem;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: var(--admin-primary);
  border-radius: 1.5px;
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
  animation: fadeIn 0.5s ease forwards;
}

/* Admin Cards */
.admin-card {
  background-color: rgba(255, 255, 255, 0.03);
  border-radius: 15px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--admin-border);
}

.card-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  color: var(--admin-light);
  position: relative;
  padding-bottom: 0.8rem;
}

.card-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 3px;
  background-color: var(--admin-primary);
  border-radius: 1.5px;
}

/* Filter Controls */
.filter-controls {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.search-box {
  position: relative;
  flex: 1;
  max-width: 300px;
}

.search-box input {
  width: 100%;
  padding: 0.8rem 1rem 0.8rem 2.5rem;
  background-color: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--admin-border);
  border-radius: 8px;
  color: var(--admin-light);
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.search-box input:focus {
  border-color: var(--admin-primary);
  outline: none;
  box-shadow: 0 0 0 2px rgba(255, 48, 48, 0.2);
}

.search-box i {
  position: absolute;
  left: 0.8rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--admin-gray);
}

.filter-dropdown select {
  padding: 0.8rem 1rem;
  background-color: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--admin-border);
  border-radius: 8px;
  color: var(--admin-light);
  font-size: 0.9rem;
  transition: all 0.3s ease;
  cursor: pointer;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%23a0a0a0' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: calc(100% - 0.8rem) center;
  padding-right: 2.5rem;
}

.filter-dropdown select:focus {
  border-color: var(--admin-primary);
  outline: none;
  box-shadow: 0 0 0 2px rgba(255, 48, 48, 0.2);
}

/* Data Table */
.data-table-container {
  overflow-x: auto;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 1rem;
}

.data-table th,
.data-table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid var(--admin-border);
}

.data-table th {
  font-weight: 600;
  color: var(--admin-light);
  background-color: rgba(255, 255, 255, 0.02);
}

.data-table tbody tr {
  transition: background-color 0.3s ease;
}

.data-table tbody tr:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.data-table .status-badge {
  display: inline-block;
  padding: 0.3rem 0.8rem;
  border-radius: 50px;
  font-size: 0.8rem;
  font-weight: 500;
}

.data-table .status-new {
  background-color: rgba(52, 152, 219, 0.2);
  color: #3498db;
}

.data-table .status-processed {
  background-color: rgba(46, 204, 113, 0.2);
  color: #2ecc71;
}

.data-table .action-btn {
  background: none;
  border: none;
  color: var(--admin-gray);
  cursor: pointer;
  transition: color 0.3s ease;
  font-size: 1rem;
  margin-right: 0.5rem;
}

.data-table .action-btn:hover {
  color: var(--admin-light);
}

.data-table .action-btn.view:hover {
  color: var(--admin-info);
}

.data-table .action-btn.delete:hover {
  color: var(--admin-danger);
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 3rem 0;
  color: var(--admin-gray);
  display: none;
}

.empty-state i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

/* Modal */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 1100;
  overflow-y: auto;
  padding: 2rem 0;
}

.modal-content {
  background-color: var(--admin-dark);
  border-radius: 15px;
  max-width: 700px;
  margin: 0 auto;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
  border: 1px solid var(--admin-border);
  animation: modalFadeIn 0.3s ease forwards;
}

.modal-header {
  padding: 1.5rem 2rem;
  border-bottom: 1px solid var(--admin-border);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--admin-light);
  margin: 0;
}

.close-modal {
  background: none;
  border: none;
  color: var(--admin-gray);
  font-size: 1.8rem;
  cursor: pointer;
  transition: color 0.3s ease;
  line-height: 1;
}

.close-modal:hover {
  color: var(--admin-danger);
}

.modal-body {
  padding: 2rem;
}

.modal-footer {
  padding: 1.5rem 2rem;
  border-top: 1px solid var(--admin-border);
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Detail Items */
.detail-item {
  margin-bottom: 1.5rem;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-weight: 500;
  color: var(--admin-primary);
  margin-bottom: 0.5rem;
  display: block;
}

.detail-value {
  color: var(--admin-light);
  background-color: rgba(255, 255, 255, 0.03);
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid var(--admin-border);
}

/* Responsive */
@media (max-width: 768px) {
  .filter-controls {
    flex-direction: column;
  }

  .search-box {
    max-width: 100%;
  }

  .data-table th,
  .data-table td {
    padding: 0.8rem;
  }

  .modal-content {
    margin: 0 1rem;
  }
}
