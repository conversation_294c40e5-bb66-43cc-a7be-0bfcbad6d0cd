# 🐛 Kontaktformular Debugging Guide

## 🔍 **Problem-Diagnose:**

### **Schritt 1: PHP-Funktionalität testen**
```
URL: https://www.heartsoulmedia.de/test_form.php
Methode: POST-Request mit Test-Daten
```

**Test-Aufruf:**
```bash
curl -X POST https://www.heartsoulmedia.de/test_form.php \
  -d "form_type=contact" \
  -d 'form_data={"name":"Test","email":"<EMAIL>","subject":"Test","message":"Test message"}'
```

### **Schritt 2: Browser DevTools verwenden**
1. Öffnen Sie https://www.heartsoulmedia.de/kontakt.html
2. F12 → Network Tab
3. Formular ausfüllen und absenden
4. Prüfen Sie die Netzwerk-Requests

### **Schritt 3: Console-Logs prüfen**
1. F12 → Console Tab
2. Formular absenden
3. Prüfen Sie auf JavaScript-Fehler

## 🛠️ **Implementierte Lösungen:**

### **1. Verbesserte Fehlerbehandlung**
- Detaillierte Fehlermeldungen
- Network-Error-Handling
- Button-State-Management

### **2. Fallback-Mechanismus**
- Mailto-Link als Backup
- Direkte E-Mail-Weiterleitung
- Benutzerfreundliche Alternative

### **3. Form-Attribute hinzugefügt**
```html
<form id="contact-form" class="contact-form" action="send_form.php" method="POST">
```

### **4. Debug-Script erstellt**
- `test_form.php` für Server-Tests
- PHP-Version und Mail-Funktion prüfen
- Detaillierte Debug-Informationen

## 🚨 **Häufige Probleme und Lösungen:**

### **Problem: 500 Internal Server Error**
**Ursachen:**
- PHP-Syntax-Fehler
- Fehlende Dateirechte (644 für PHP-Dateien)
- Server-Konfigurationsprobleme

**Lösung:**
```bash
# Dateirechte prüfen
chmod 644 send_form.php
chmod 644 test_form.php

# PHP-Logs prüfen
tail -f /path/to/php_errors.log
```

### **Problem: Mail-Funktion nicht verfügbar**
**Ursachen:**
- Hosting-Provider blockiert mail() Funktion
- SMTP nicht konfiguriert
- Sendmail nicht installiert

**Lösung:**
- Hosting-Provider kontaktieren
- SMTP-Konfiguration anfordern
- Alternative Mail-Services verwenden (PHPMailer, etc.)

### **Problem: JavaScript-Fehler**
**Ursachen:**
- Fetch API nicht unterstützt
- CORS-Probleme
- Network-Timeouts

**Lösung:**
- Browser-Kompatibilität prüfen
- CORS-Header in PHP setzen
- Timeout-Handling verbessern

## 📧 **E-Mail-Konfiguration prüfen:**

### **Server-Requirements:**
```php
// Prüfen ob mail() verfügbar ist
if (function_exists('mail')) {
    echo "Mail function available";
} else {
    echo "Mail function NOT available";
}

// SMTP-Konfiguration prüfen
$smtp_settings = ini_get_all('mail');
print_r($smtp_settings);
```

### **Alternative E-Mail-Methoden:**
1. **PHPMailer** (empfohlen für SMTP)
2. **SwiftMailer** (moderne Alternative)
3. **Mail-APIs** (SendGrid, Mailgun, etc.)

## 🔧 **Debugging-Commands:**

### **Server-Logs prüfen:**
```bash
# PHP Error Log
tail -f /var/log/php_errors.log

# Apache Error Log
tail -f /var/log/apache2/error.log

# Nginx Error Log
tail -f /var/log/nginx/error.log
```

### **Network-Test:**
```bash
# Test PHP-Script direkt
curl -I https://www.heartsoulmedia.de/send_form.php

# Test mit POST-Daten
curl -X POST https://www.heartsoulmedia.de/send_form.php \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "form_type=contact&form_data={}"
```

## ✅ **Erfolgreiche Konfiguration prüfen:**

### **Checkliste:**
- [ ] PHP-Script lädt ohne Fehler
- [ ] mail() Funktion verfügbar
- [ ] Dateirechte korrekt (644)
- [ ] JavaScript lädt ohne Fehler
- [ ] Network-Request erfolgreich
- [ ] E-Mail wird empfangen

### **Test-Formular:**
Nach dem Upload testen Sie:
1. https://www.heartsoulmedia.de/test_form.php (Debug-Info)
2. https://www.heartsoulmedia.de/kontakt.html (Vollständiges Formular)

## 📞 **Support:**
Bei anhaltenden Problemen:
- Hosting-Provider Support kontaktieren
- PHP-Konfiguration prüfen lassen
- SMTP-Einstellungen anfordern
