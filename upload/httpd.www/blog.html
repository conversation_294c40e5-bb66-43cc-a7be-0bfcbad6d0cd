<!doctype html>
<html lang="de">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Blog | Heart & Soul Film- und Medienproduktion</title>
  <link rel="stylesheet" href="css/style_fixed.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/locomotive-scroll@4.1.4/dist/locomotive-scroll.min.css">
  <link rel="stylesheet" href="css/locomotive-scroll-basic.css">
  <link rel="stylesheet" href="css/animations.css">
  <link rel="stylesheet" href="css/service-pages.css">
  <meta name="description" content="Blog der Heart & Soul Film- und Medienproduktion - Erfahren Sie mehr über Filmproduktion, Videotechniken und Branchentrends.">

  <link rel="icon" href="Hs-favicon.webp" type="image/webp">
  <link rel="icon" href="/favicon.ico" sizes="any">
  <link rel="icon" href="/icon.svg" type="image/svg+xml">
  <link rel="apple-touch-icon" href="icon.png">

  <link rel="manifest" href="site.webmanifest">
  <meta name="theme-color" content="#121212">

  <style>
    /* Blog Page Specific Styles */
    .blog-section {
      padding: 6rem 0;
    }

    .blog-container {
      max-width: 900px;
      margin: 0 auto;
      padding: 0 2rem;
    }

    .blog-header {
      text-align: center;
      margin-bottom: 4rem;
    }

    .blog-title {
      font-size: 2.8rem;
      font-weight: 700;
      margin-bottom: 1rem;
      color: var(--text-light);
    }

    .blog-description {
      font-size: 1.2rem;
      color: var(--text-gray);
      max-width: 700px;
      margin: 0 auto;
      line-height: 1.6;
    }

    .blog-cards {
      display: flex;
      flex-direction: column;
      gap: 3rem;
    }

    .blog-card {
      background: rgba(255, 255, 255, 0.03);
      border-radius: 30px;
      overflow: hidden;
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      border: 1px solid rgba(255, 255, 255, 0.05);
      display: flex;
      flex-direction: column;
    }

    .blog-card:hover {
      transform: translateY(-10px);
      box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
    }

    .blog-card-image {
      width: 100%;
      height: 300px;
      overflow: hidden;
      position: relative;
    }

    .blog-card-image img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: transform 0.5s ease;
    }

    .blog-card:hover .blog-card-image img {
      transform: scale(1.05);
    }

    .blog-card-content {
      padding: 2.5rem;
      display: flex;
      flex-direction: column;
      flex-grow: 1;
    }

    .blog-card-date {
      font-size: 0.9rem;
      color: var(--primary-color);
      margin-bottom: 1rem;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .blog-card-title {
      font-size: 1.8rem;
      font-weight: 700;
      margin-bottom: 1.5rem;
      color: var(--text-light);
      line-height: 1.3;
    }

    .blog-card-excerpt {
      font-size: 1.1rem;
      color: var(--text-gray);
      margin-bottom: 2rem;
      line-height: 1.7;
      flex-grow: 1;
    }

    .blog-card-link {
      align-self: flex-start;
    }

    .blog-pagination {
      display: flex;
      justify-content: center;
      gap: 1rem;
      margin-top: 4rem;
    }

    .page-number {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: rgba(255, 255, 255, 0.05);
      color: var(--text-light);
      text-decoration: none;
      transition: all 0.3s ease;
    }

    .page-number:hover, .page-number.active {
      background-color: var(--primary-color);
    }

    .page-arrow {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: rgba(255, 255, 255, 0.05);
      color: var(--text-light);
      text-decoration: none;
      transition: all 0.3s ease;
    }

    .page-arrow:hover {
      background-color: var(--primary-color);
    }

    .blog-categories {
      display: flex;
      flex-wrap: wrap;
      gap: 0.8rem;
      margin-bottom: 1.5rem;
    }

    .blog-category {
      background-color: rgba(255, 48, 48, 0.1);
      color: var(--primary-color);
      padding: 0.4rem 1rem;
      border-radius: 50px;
      font-size: 0.9rem;
      font-weight: 500;
      transition: all 0.3s ease;
    }

    .blog-category:hover {
      background-color: var(--primary-color);
      color: white;
    }

    @media (max-width: 768px) {
      .blog-section {
        padding: 4rem 0;
      }

      .blog-container {
        padding: 0 1.5rem;
      }

      .blog-title {
        font-size: 2.2rem;
      }

      .blog-card-image {
        height: 220px;
      }

      .blog-card-content {
        padding: 1.8rem;
      }

      .blog-card-title {
        font-size: 1.5rem;
      }
    }
  </style>
</head>

<body data-scroll-container>
  <!-- Navigation -->
  <nav class="navbar">
    <div class="navbar-container">
      <div class="logo">
        <a href="index.html"><img src="HeartAndSoul-Logo.png" alt="Heart & Soul Medienproduktion"></a>
      </div>
      <div class="nav-links">
        <a href="index.html">Home</a>
        <a href="portfolio.html">Portfolio</a>
        <a href="leistungen.html">Leistungen</a>
        <a href="kontakt.html">Kontakt</a>
      </div>
      <button class="mobile-menu-btn">
        <i class="fas fa-bars"></i>
      </button>
    </div>
  </nav>
  <!-- Mobile Menu (separate from navbar for better structure) -->
  <div class="mobile-menu">
    <a href="index.html">Home</a>
    <a href="portfolio.html">Portfolio</a>
    <a href="leistungen.html">Leistungen</a>
    <a href="kontakt.html">Kontakt</a>
  </div>

  <!-- Hero Section -->
  <section class="hero-section" data-scroll-section style="background-image: linear-gradient(to bottom, rgba(18, 18, 18, 0.8), rgba(30, 30, 30, 0.9)), url('img/BTS4.jpeg');">
    <div class="hero-content">
      <h1 class="page-title">Blog</h1>
      <p class="page-subtitle">Einblicke, Tipps und Neuigkeiten aus der Welt der Filmproduktion</p>
    </div>
  </section>

  <!-- Main Content Container -->
  <div class="content-container">
    <!-- Blog Section -->
    <section class="blog-section" data-scroll-section>
      <div class="blog-container">
        <div class="blog-header">
          <h2 class="blog-title">Unser <span style="color: var(--primary-color);">Blog</span></h2>
          <p class="blog-description">Hier teilen wir unser Wissen, Erfahrungen und Einblicke in die Welt der professionellen Videoproduktion. Erfahren Sie mehr über Trends, Techniken und Erfolgsgeschichten.</p>
        </div>

        <div class="blog-cards">
          <!-- Blog Card 1 -->
          <article class="blog-card">
            <div class="blog-card-image">
              <img src="img/BTS1.jpg" alt="Videomarketing für Marketing-Manager">
            </div>
            <div class="blog-card-content">
              <div class="blog-card-date">
                <i class="far fa-calendar-alt"></i> 15. Juli 2024
              </div>
              <div class="blog-categories">
                <span class="blog-category">Videomarketing</span>
                <span class="blog-category">Marketing</span>
              </div>
              <h3 class="blog-card-title">10 Gründe, warum Sie als Marketing-Manager jetzt mit Videomarketing starten sollten</h3>
              <p class="blog-card-excerpt">Videomarketing ist längst kein Trend mehr – es ist ein Muss. Ob Sie Leads generieren, Ihre Marke stärken oder komplexe Produkte erklären wollen: Videos liefern messbare Ergebnisse. Für Marketingverantwortliche in Unternehmen bietet Videocontent die perfekte Schnittstelle zwischen Information und Emotion.

In diesem Beitrag erfahren Sie, warum Videomarketing 2025 zur Pflichtdisziplin im Marketing-Mix gehört – und wie Sie konkret davon profitieren.</p>
              <a href="blog-post.html" class="btn btn-outline blog-card-link">Weiterlesen</a>
            </div>
          </article>
        </div>

        <!-- Pagination -->
        <div class="blog-pagination">
          <a href="#" class="page-arrow"><i class="fas fa-chevron-left"></i></a>
          <a href="#" class="page-number active">1</a>
          <a href="#" class="page-number">2</a>
          <a href="#" class="page-number">3</a>
          <a href="#" class="page-arrow"><i class="fas fa-chevron-right"></i></a>
        </div>
      </div>
    </section>

    <!-- Call to Action Section -->
    <section class="cta-section" data-scroll-section>
      <div class="cta-container">
        <h2 class="section-title">Bereit für Ihren <span style="color: var(--primary-color);">eigenen Film?</span></h2>
        <div class="cta-content">
          <p class="cta-text text-center">Lassen Sie uns gemeinsam Ihre Vision zum Leben erwecken und Ihre Geschichte erzählen.</p>
          <h3 class="cta-subheadline">Kostenloses Erstgespräch</h3>
          <div class="cta-buttons-centered">
            <a href="meeting.html" class="btn btn-primary btn-large">Termin anfragen</a>
            <a href="kontakt.html" class="btn btn-outline btn-large">Kontakt aufnehmen</a>
          </div>
        </div>
      </div>
    </section>
  </div>

  <!-- Footer -->
  <footer class="footer" data-scroll-section>
    <div class="footer-container">
      <div>
        <div class="footer-logo">
          <img src="HeartAndSoul-Logo.png" alt="Heart & Soul Medienproduktion">
        </div>
        <p class="footer-description">Professionelle Videoproduktion mit Herz und Seele. Wir bringen Ihre Botschaft zum Leben.</p>
        <div class="social-links">
          <a href="#" class="social-link"><i class="fab fa-facebook-f"></i></a>
          <a href="#" class="social-link"><i class="fab fa-instagram"></i></a>
          <a href="#" class="social-link"><i class="fab fa-youtube"></i></a>
          <a href="https://www.linkedin.com/in/malte-strömsdörfer-998b55112" class="social-link" target="_blank" rel="noopener"><i class="fab fa-linkedin-in"></i></a>
        </div>
      </div>
      <div>
        <h3 class="footer-title">Leistungen</h3>
        <ul class="footer-links">
          <li><a href="werbefilm.html">Werbefilme</a></li>
          <li><a href="imagefilm.html">Imagefilme</a></li>
          <li><a href="recruiting.html">Recruitingfilme</a></li>
          <li><a href="leistungen.html">Alle Leistungen</a></li>
          <li><a href="blog.html" class="active">Blog</a></li>
        </ul>
      </div>
      <div>
        <h3 class="footer-title">Kontakt</h3>
        <ul class="footer-links">
          <li><i class="fas fa-phone mr-2"></i> <a href="tel:+4917650200617">+49 (0) 176 50200617</a></li>
          <li><i class="fas fa-envelope mr-2"></i> <a href="mailto:<EMAIL>"><EMAIL></a></li>
        </ul>      </div>
    </div>
    <div class="copyright">
      <div class="footer-legal">
        <a href="impressum.html">Impressum</a>
        <a href="datenschutz.html">Datenschutz</a>
        <a href="agb.html">AGB</a>
      </div>
      <div class="copyright-text">
        &copy; 2024 Heart & Soul Medienproduktion UG (haftungsbeschränkt). Alle Rechte vorbehalten.
        
      </div>
    </div>
  </footer>

  <!-- Cookie Consent Banner -->
  <div id="cookie-banner" class="cookie-banner">
    <p>Diese Website verwendet Cookies, um Ihnen ein besseres Nutzungserlebnis zu bieten. Mit der Nutzung unserer Website stimmen Sie der Verwendung von Cookies gemäß unserer <a href="datenschutz.html">Datenschutzerklärung</a> zu.</p>
    <div class="cookie-buttons">
      <button id="cookie-decline" class="cookie-btn cookie-btn-decline">Ablehnen</button>
      <button id="cookie-accept" class="cookie-btn cookie-btn-accept">Akzeptieren</button>
    </div>
  </div>

  <!-- Scripts -->
  <script src="https://cdn.jsdelivr.net/npm/locomotive-scroll@4.1.4/dist/locomotive-scroll.min.js"></script>
  <script src="js/app.js"></script>
  <script src="js/smooth-scroll-basic.js"></script>
  <script src="js/cookie-consent.js"></script>
</body>

</html>
