/* Portfolio Page Styles */

.portfolio-section {
  padding: 5rem 0;
}

.portfolio-intro {
  max-width: 900px;
  margin: 0 auto 3rem;
  text-align: center;
}

.portfolio-intro p {
  font-size: 1.3rem;
  line-height: 1.7;
  color: var(--text-light);
}



/* Portfolio Grid */
.portfolio-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2.5rem;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 1rem;
}

.portfolio-item {
  background-color: var(--dark-secondary);
  border-radius: 15px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 100%;
}

.portfolio-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

.portfolio-video {
  width: 100%;
  border-radius: 15px;
  overflow: hidden;
  aspect-ratio: 16 / 9;
}



/* Responsive Styles */
@media (max-width: 1200px) {
  .portfolio-grid {
    gap: 2rem;
  }
}

@media (max-width: 992px) {
  .portfolio-section {
    padding: 4rem 2rem;
  }

  .portfolio-grid {
    gap: 1.5rem;
  }

  .portfolio-intro p {
    font-size: 1.2rem;
  }
}

@media (max-width: 768px) {
  .portfolio-section {
    padding: 3rem 1.5rem;
  }

  .portfolio-grid {
    grid-template-columns: 1fr;
    max-width: 500px;
    gap: 2rem;
  }


}

/* Plyr Video Player Customization for Portfolio */
.portfolio-video .plyr {
  border-radius: 0;
}

.portfolio-video .plyr--video {
  background-color: var(--dark-bg);
}

/* Video Poster Image */
.portfolio-video video {
  width: 100%;
  height: auto;
  display: block;
}
