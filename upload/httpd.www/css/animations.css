/* Fade-in animations for scroll effects */

/* Base styles for animated elements */
.fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 0.8s ease, transform 0.8s ease;
}

.fade-in.is-inview {
  opacity: 1;
  transform: translateY(0);
}

/* Staggered animations for multiple elements */
.fade-in-stagger:nth-child(1) { transition-delay: 0s; }
.fade-in-stagger:nth-child(2) { transition-delay: 0.1s; }
.fade-in-stagger:nth-child(3) { transition-delay: 0.2s; }
.fade-in-stagger:nth-child(4) { transition-delay: 0.3s; }
.fade-in-stagger:nth-child(5) { transition-delay: 0.4s; }
.fade-in-stagger:nth-child(6) { transition-delay: 0.5s; }
.fade-in-stagger:nth-child(7) { transition-delay: 0.6s; }
.fade-in-stagger:nth-child(8) { transition-delay: 0.7s; }

/* Fade in from left */
.fade-in-left {
  opacity: 0;
  transform: translateX(-30px);
  transition: opacity 0.8s ease, transform 0.8s ease;
}

.fade-in-left.is-inview {
  opacity: 1;
  transform: translateX(0);
}

/* Fade in from right */
.fade-in-right {
  opacity: 0;
  transform: translateX(30px);
  transition: opacity 0.8s ease, transform 0.8s ease;
}

.fade-in-right.is-inview {
  opacity: 1;
  transform: translateX(0);
}

/* Fade in with scale */
.fade-in-scale {
  opacity: 0;
  transform: scale(0.9);
  transition: opacity 0.8s ease, transform 0.8s ease;
}

.fade-in-scale.is-inview {
  opacity: 1;
  transform: scale(1);
}

/* Parallax effect for background images */
.parallax-bg {
  transition: transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}
