# FTP Upload Instructions für www.heartsoulmedia.de

## Ordner-Struktur auf dem FTP-Server:

### 🌐 httpd.www (Öffentlicher Web-Ordner)
**Alle Dateien, die über das Web zugänglich sein sollen:**

- Alle .html Dateien (index.html, portfolio.html, etc.)
- .htaccess
- send_form.php
- robots.txt, sitemap.xml, site.webmanifest
- favicon.ico, favicon.png, Hs-favicon.webp
- HeartAndSoul-Logo.png, HEART_Soul.svg
- under-construction.html
- css/ (kompletter Ordner)
- js/ (kompletter Ordner)
- img/ (kompletter Ordner)
- videos/ (kompletter Ordner)
- images/ (kompletter Ordner)
- admin/login.html
- admin/dashboard.html
- admin/css/ (kompletter Ordner)
- admin/js/ (kompletter Ordner)
- admin/.htaccess

### 🔒 httpd.private (Privater Ordner - nicht web-zugänglich)
**Sensible Dateien, die NICHT über das Web erreichbar sein sollen:**

- admin/auth.php
- admin/dashboard.php
- admin/login.php
- admin/reset-password.php
- admin/send_submissions.php
- admin/schema.sql
- admin/README.md
- admin/SECURITY.md
- logs/ (Ordner für Log-Dateien)
- backups/ (Ordner für Backup-Dateien)
  - .htaccess-under-construction
  - .htaccess-under-construction-backup

## Wichtige Anpassungen:

1. **Log-Pfad**: send_form.php wurde angepasst, um Logs in ../httpd.private/logs/ zu schreiben
2. **Admin-Sicherheit**: .htaccess in admin/ verhindert direkten Zugriff auf PHP-Dateien
3. **Backup-Dateien**: .htaccess-Backup-Dateien sind im privaten Ordner sicher aufbewahrt

## Upload-Reihenfolge:

1. Zuerst httpd.private Ordner mit allen sensiblen Dateien
2. Dann httpd.www Ordner mit allen öffentlichen Dateien
3. Dateirechte prüfen: 644 für Dateien, 755 für Ordner
4. Website testen: www.heartsoulmedia.de

## Sicherheitshinweise:

- PHP-Dateien im admin/ Ordner sind durch .htaccess geschützt
- Log-Dateien werden im privaten Ordner gespeichert
- Backup-Dateien sind nicht öffentlich zugänglich
- Under Construction Modus ist deaktiviert
