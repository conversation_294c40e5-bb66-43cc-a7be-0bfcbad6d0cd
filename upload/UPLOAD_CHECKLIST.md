# ✅ Upload-Checkliste für www.heartsoulmedia.de

## 📊 Datei-Übersicht:

### 🌐 httpd.www (<PERSON>ffentlich):
- ✅ 21 HTML-Dateien
- ✅ 11 CSS-Dateien
- ✅ 8 JavaScript-Dateien
- ✅ 15 Video-Dateien (.mp4)
- ✅ 10 Bild-Dateien
- ✅ 1 .htaccess (Hauptkonfiguration)
- ✅ 1 send_form.php (angepasst)
- ✅ <PERSON>avi<PERSON>, Manifest, Robots.txt
- ✅ Admin-Frontend (HTML)

### 🔒 httpd.private (Privat):
- ✅ 5 Admin-PHP-Dateien
- ✅ 1 Datenbank-Schema (.sql)
- ✅ 2 Dokumentations-Dateien (.md)
- ✅ 2 Backup-Dateien (.htaccess)
- ✅ 1 .htaccess (Zugriffschutz)
- ✅ 1 Log-Datei (vorbereitet)

## 🚀 Upload-Schritte:

### Schritt 1: httpd.private hochladen
```
FTP: /httpd.private/
Quelle: upload/httpd.private/*
```

### Schritt 2: httpd.www hochladen  
```
FTP: /httpd.www/
Quelle: upload/httpd.www/*
```

### Schritt 3: Dateire<PERSON>e setzen
```
Dateien: 644
Ordner: 755
.htaccess: 644
```

### Schritt 4: Website testen
```
URL: https://www.heartsoulmedia.de
Admin: https://www.heartsoulmedia.de/admin/login.html
```

## 🔍 Test-Checkliste:

- [ ] Hauptseite lädt korrekt
- [ ] Portfolio-Videos funktionieren
- [ ] Kontaktformular sendet E-Mails
- [ ] Admin-Login ist erreichbar
- [ ] HTTPS-Weiterleitung funktioniert
- [ ] Mobile Ansicht ist responsive
- [ ] Alle Bilder laden korrekt
- [ ] Navigation funktioniert

## 🆘 Troubleshooting:

**Problem: 500 Internal Server Error**
- Dateirechte prüfen (644/755)
- .htaccess Syntax überprüfen
- PHP-Logs in httpd.private/logs/ prüfen

**Problem: Admin nicht erreichbar**
- PHP-Dateien in httpd.private/ prüfen
- .htaccess in admin/ überprüfen

**Problem: Formulare funktionieren nicht**
- send_form.php Pfade überprüfen
- E-Mail-Konfiguration prüfen

## 📞 Support:
<EMAIL>
