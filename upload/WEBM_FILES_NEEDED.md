# 🎬 WebM Video Files Required

## 📋 **Benötigte WebM-Dateien:**

Die folgenden MP4-<PERSON><PERSON> m<PERSON> in WebM konvertiert und hochgeladen werden:

### **Index-Seite (4 Videos):**
1. `Heart-and-Soul-Hero.webm` (Hero-Video)
2. `featured-1.webm` (Portfolio-Video 1)
3. `thueros_bbq-1080p.webm` (Portfolio-Video 2)
4. `featured-3.webm` (Portfolio-Video 3)

### **Portfolio-Seite (10 Videos):**
1. `featured-1.webm`
2. `featured-2.webm`
3. `featured-3.webm`
4. `<PERSON><PERSON> GmbH - Love Haftmaterial.webm`
5. `<PERSON><PERSON>.webm`
6. `ib medical Recruitingfilm.webm`
7. `K&P Kanzlei Video.webm`
8. `kanzleistories 1080p.webm`
9. `MS Horse Produktfilm.webm`
10. `Thüros_Multiking.webm`

## 🔄 **Konvertierungs-Commands:**

### **FFmpeg Konvertierung (empfohlen):**
```bash
# Einzelne Datei konvertieren
ffmpeg -i "input.mp4" -c:v libvpx-vp9 -crf 30 -b:v 0 -b:a 128k -c:a libopus "output.webm"

# Batch-Konvertierung aller MP4-Dateien
for file in *.mp4; do
    ffmpeg -i "$file" -c:v libvpx-vp9 -crf 30 -b:v 0 -b:a 128k -c:a libopus "${file%.mp4}.webm"
done
```

### **Optimierte Einstellungen:**
```bash
# Für bessere Qualität (größere Dateien)
ffmpeg -i "input.mp4" -c:v libvpx-vp9 -crf 23 -b:v 0 -b:a 128k -c:a libopus "output.webm"

# Für kleinere Dateien (geringere Qualität)
ffmpeg -i "input.mp4" -c:v libvpx-vp9 -crf 35 -b:v 0 -b:a 96k -c:a libopus "output.webm"
```

## 📁 **Upload-Struktur:**

```
httpd.www/videos/
├── Heart-and-Soul-Hero.webm
├── Heart-and-Soul-Hero.mp4 (Fallback)
├── featured-1.webm
├── featured-1.mp4 (Fallback)
├── featured-2.webm
├── featured-2.mp4 (Fallback)
├── featured-3.webm
├── featured-3.mp4 (Fallback)
├── thueros_bbq-1080p.webm
├── thueros_bbq-1080p.mp4 (Fallback)
├── Herma GmbH - Love Haftmaterial.webm
├── Herma GmbH - Love Haftmaterial.mp4 (Fallback)
├── Hobby Himmel.webm
├── Hobby Himmel.mp4 (Fallback)
├── ib medical Recruitingfilm.webm
├── ib medical Recruitingfilm.mp4 (Fallback)
├── K&P Kanzlei Video.webm
├── K&P Kanzlei Video.mp4 (Fallback)
├── kanzleistories 1080p.webm
├── kanzleistories 1080p.mp4 (Fallback)
├── MS Horse Produktfilm.webm
├── MS Horse Produktfilm.mp4 (Fallback)
├── Thüros_Multiking.webm
└── Thüros_Multiking.mp4 (Fallback)
```

## 🚀 **Vorteile von WebM:**

### **Performance:**
- **30-50% kleinere Dateien** als MP4
- **Bessere Kompression** bei gleicher Qualität
- **Schnellere Ladezeiten**

### **Browser-Support:**
- ✅ Chrome, Firefox, Edge, Opera
- ✅ Android Browser
- ❌ Safari (daher MP4-Fallback)

### **Qualität:**
- **VP9-Codec** für bessere Kompression
- **Opus-Audio** für kleinere Audiodateien
- **Verlustfreie Qualität** möglich

## 📊 **Erwartete Dateigrößen-Reduktion:**

| Original MP4 | Erwartete WebM | Einsparung |
|--------------|----------------|------------|
| 24MB | ~12-16MB | 33-50% |
| 12MB | ~6-8MB | 33-50% |
| 9MB | ~4-6MB | 33-50% |
| 3.6MB | ~2-2.5MB | 30-45% |

## ⚠️ **Wichtige Hinweise:**

1. **Fallback beibehalten**: MP4-Dateien NICHT löschen (Safari-Support)
2. **Dateinamen**: Exakt gleiche Namen wie MP4-Dateien
3. **Qualität prüfen**: WebM-Dateien vor Upload testen
4. **Browser-Test**: Funktionalität in verschiedenen Browsern prüfen

## 🔧 **Online-Konverter (Alternative):**

Falls FFmpeg nicht verfügbar:
- CloudConvert.com
- Online-Convert.com
- Convertio.co

**Einstellungen:** VP9-Codec, Opus-Audio, CRF 30
