# Upload-<PERSON><PERSON><PERSON> für www.heartsoulmedia.de

## 📁 Ordner-Struktur:

### 🌐 httpd.www/
**Öffentlicher Web-Ordner - Alle Dateien hier hochladen**
- Alle Website-Dateien, die über das Web zugänglich sein sollen
- HTML, CSS, JS, Bilder, Videos
- Admin-Frontend (login.html, dashboard.html)

### 🔒 httpd.private/
**Privater Ordner - Nicht web-zugänglich**
- Sensible PHP-Dateien
- Datenbank-Schema
- Log-Dateien
- Backup-Dateien

## 🚀 Upload-Anweisungen:

1. **httpd.private Ordner zuerst hochladen**
   - Kompletten Inhalt von `upload/httpd.private/` nach `httpd.private/` auf dem Server

2. **httpd.www Ordner danach hochladen**
   - Kompletten Inhalt von `upload/httpd.www/` nach `httpd.www/` auf dem Server

3. **Dateirechte setzen:**
   - Dateien: 644
   - Ordner: 755
   - .htaccess: 644

4. **Website testen:**
   - https://www.heartsoulmedia.de

## ✅ Enthaltene Dateien:

### httpd.www (Öffentlich):
- ✅ Alle HTML-Seiten
- ✅ CSS, JavaScript, Bilder, Videos
- ✅ .htaccess (Hauptkonfiguration)
- ✅ send_form.php (angepasst für private Logs)
- ✅ Favicon, Manifest, Robots.txt
- ✅ Admin-Frontend (HTML, CSS, JS)

### httpd.private (Privat):
- ✅ Admin-PHP-Dateien
- ✅ Datenbank-Schema
- ✅ Log-Ordner (vorbereitet)
- ✅ Backup-Dateien
- ✅ .htaccess (Zugriff verweigern)

## 🔒 Sicherheit:
- PHP-Dateien sind im privaten Bereich geschützt
- Log-Dateien sind nicht web-zugänglich
- Admin-Bereich ist zusätzlich abgesichert
- Under Construction Modus ist deaktiviert

## 📞 Support:
Bei Problemen: <EMAIL>
