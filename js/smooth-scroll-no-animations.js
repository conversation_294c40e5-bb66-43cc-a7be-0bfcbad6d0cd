document.addEventListener('DOMContentLoaded', function() {
  // Initialize Locomotive Scroll
  const scroll = new LocomotiveScroll({
    el: document.querySelector('[data-scroll-container]'),
    smooth: true,
    smoothMobile: false,
    inertia: 0.65, // Increased for more pronounced parallax effect
    getDirection: true,
    reloadOnContextChange: true,
    lerp: 0.07, // Adjusted for smoother parallax
    multiplier: 0.9, // Slightly reduced for better control
    touchMultiplier: 2.5,
    scrollFromAnywhere: true,
    resetNativeScroll: true,
    smartphone: {
      smooth: false
    },
    tablet: {
      smooth: true,
      breakpoint: 1024
    }
  });

  // Fix for hero video - ensure it's properly loaded
  const heroVideo = document.querySelector('.hero-video');
  if (heroVideo) {
    heroVideo.addEventListener('loadeddata', () => {
      scroll.update();
    });
  }

  // Add subtle parallax effect to sections
  const sections = document.querySelectorAll('[data-scroll-section]');
  sections.forEach((section, index) => {
    // Add different data-scroll-speed to create depth
    section.setAttribute('data-scroll-speed', (index % 2 === 0) ? '0.1' : '0.05');

    // Add data-scroll to enable parallax
    section.setAttribute('data-scroll', '');
  });

  // Add parallax effect to section titles
  const sectionTitles = document.querySelectorAll('.section-title');
  sectionTitles.forEach(title => {
    title.setAttribute('data-scroll', '');
    title.setAttribute('data-scroll-speed', '0.2');
  });

  // Add parallax effect to content containers
  const contentContainers = document.querySelectorAll('.welcome-content, .welcome-features, .video-container, .product-grid, .cta-content');
  contentContainers.forEach((container, index) => {
    container.setAttribute('data-scroll', '');
    container.setAttribute('data-scroll-speed', '0.15');
  });

  // Add parallax effect to individual elements
  const parallaxElements = document.querySelectorAll('.product-card, .feature-item, .video-item');
  parallaxElements.forEach((element, index) => {
    element.setAttribute('data-scroll', '');
    // Alternate speeds for more natural feel
    const speed = 0.1 + (index % 3) * 0.05;
    element.setAttribute('data-scroll-speed', speed.toString());
  });

  // Add parallax effect to hero elements
  const heroTitle = document.querySelector('.hero-overlay h1');
  if (heroTitle) {
    heroTitle.setAttribute('data-scroll', '');
    heroTitle.setAttribute('data-scroll-speed', '0.3');
  }

  const heroSubtitle = document.querySelector('.hero-overlay p');
  if (heroSubtitle) {
    heroSubtitle.setAttribute('data-scroll', '');
    heroSubtitle.setAttribute('data-scroll-speed', '0.2');
  }

  // Update scroll on window resize
  window.addEventListener('resize', () => {
    setTimeout(() => {
      scroll.update();
    }, 500);
  });

  // Update scroll when images and other resources are loaded
  window.addEventListener('load', () => {
    scroll.update();
  });

  // Handle navigation links
  const navLinks = document.querySelectorAll('.nav-links a, .mobile-menu a');
  navLinks.forEach(link => {
    link.addEventListener('click', function(e) {
      e.preventDefault();
      const targetId = this.getAttribute('href');

      // If it's a hash link, scroll to the section
      if (targetId.startsWith('#')) {
        const targetSection = document.querySelector(targetId);
        if (targetSection) {
          scroll.scrollTo(targetSection);

          // Close mobile menu if open
          const mobileMenu = document.querySelector('.mobile-menu');
          if (mobileMenu && mobileMenu.classList.contains('active')) {
            mobileMenu.classList.remove('active');
          }
        }
      } else {
        // If it's a regular link, navigate to the page
        window.location.href = targetId;
      }
    });
  });

  // Add custom class when scrolling
  scroll.on('scroll', (instance) => {
    const scrollTop = instance.scroll.y;
    const navbar = document.querySelector('.navbar');

    if (navbar) {
      if (scrollTop > 50) {
        navbar.classList.add('scrolled');
      } else {
        navbar.classList.remove('scrolled');
      }
    }
  });
});
