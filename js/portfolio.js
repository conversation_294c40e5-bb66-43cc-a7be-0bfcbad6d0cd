document.addEventListener('DOMContentLoaded', function() {
  let players = [];
  
  // Lazy load videos when they come into view
  const videoObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const video = entry.target;
        
        // Initialize Plyr only when video comes into view
        if (!video.dataset.initialized) {
          const player = new Plyr(video, {
            controls: ['play-large', 'play', 'progress', 'current-time', 'mute', 'volume', 'fullscreen'],
            loadSprite: true,
            iconUrl: 'https://cdn.plyr.io/3.7.8/plyr.svg',
            blankVideo: 'https://cdn.plyr.io/static/blank.mp4',
            autoplay: false,
            autopause: true,
            seekTime: 10,
            volume: 1,
            muted: false,
            clickToPlay: true,
            disableContextMenu: false,
            hideControls: false,
            resetOnEnd: true,
            keyboard: { focused: true, global: false }
          });
          
          players.push(player);
          video.dataset.initialized = 'true';
          
          // Set up pause other videos functionality
          player.on('play', () => {
            players.forEach(otherPlayer => {
              if (otherPlayer !== player && otherPlayer.playing) {
                otherPlayer.pause();
              }
            });
          });
          
          // Load video metadata when first observed
          video.addEventListener('loadedmetadata', function() {
            video.currentTime = 0;
          });
        }
        
        // Stop observing this video after initialization
        videoObserver.unobserve(video);
      }
    });
  }, {
    rootMargin: '100px' // Start loading 100px before video enters viewport
  });

  // Observe all videos for lazy loading
  document.querySelectorAll('.portfolio-video video').forEach(video => {
    videoObserver.observe(video);
  });
});
