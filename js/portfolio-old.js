document.addEventListener('DOMContentLoaded', function() {
  let players = [];

  // Lazy load videos when they come into view
  const videoObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const video = entry.target;
        const videoContainer = video.closest('.portfolio-video');

        // Initialize Plyr only when video comes into view
        if (!video.dataset.initialized) {
          const player = new Plyr(video, {
            controls: ['play-large', 'play', 'progress', 'current-time', 'mute', 'volume', 'fullscreen'],
            loadSprite: true,
            iconUrl: 'https://cdn.plyr.io/3.7.8/plyr.svg',
            blankVideo: 'https://cdn.plyr.io/static/blank.mp4',
            autoplay: false,
            autopause: true,
            seekTime: 10,
            volume: 1,
            muted: false,
            clickToPlay: true,
            disableContextMenu: false,
            hideControls: false,
            resetOnEnd: true,
            keyboard: { focused: true, global: false }
          });

          players.push(player);
          video.dataset.initialized = 'true';

          // Set up pause other videos functionality
          player.on('play', () => {
            players.forEach(otherPlayer => {
              if (otherPlayer !== player && otherPlayer.playing) {
                otherPlayer.pause();
              }
            });
          });
        }

        // Stop observing this video
        videoObserver.unobserve(video);
      }
    });
  }, {
    rootMargin: '50px'
  });

  // Observe all videos
  document.querySelectorAll('.portfolio-video video').forEach(video => {
    videoObserver.observe(video);
  });

  // Ensure videos load to first frame
  document.querySelectorAll('.portfolio-video video').forEach(video => {
    // Set currentTime to 0 to ensure first frame is shown
    video.addEventListener('loadedmetadata', function() {
      video.currentTime = 0;
    });
  });

  // Pause all videos when a new one starts playing
  players.forEach(player => {
    player.on('play', () => {
      players.forEach(p => {
        if (p !== player && p.playing) {
          p.pause();
        }
      });
    });
  });

  // Lazy loading for videos
  const lazyLoadVideos = () => {
    const videoElements = document.querySelectorAll('.portfolio-video video');
    videoElements.forEach(video => {
      const rect = video.getBoundingClientRect();
      const isInViewport = (
        rect.top >= -200 &&
        rect.left >= -200 &&
        rect.bottom <= (window.innerHeight + 200 || document.documentElement.clientHeight + 200) &&
        rect.right <= (window.innerWidth + 200 || document.documentElement.clientWidth + 200)
      );

      if (isInViewport && !video.dataset.loaded) {
        // Mark as loaded
        video.dataset.loaded = 'true';

        // Ensure video is loaded to first frame
        if (video.readyState === 0) {
          video.load();
          video.addEventListener('loadedmetadata', function() {
            video.currentTime = 0;
          });
        } else {
          video.currentTime = 0;
        }
      }
    });
  };

  // Initial call and event listener for lazy loading
  setTimeout(lazyLoadVideos, 500); // Slight delay for initial load
  window.addEventListener('scroll', lazyLoadVideos);
  window.addEventListener('resize', lazyLoadVideos);
});
