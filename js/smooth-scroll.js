document.addEventListener('DOMContentLoaded', function() {
  // Initialize Locomotive Scroll
  const scroll = new LocomotiveScroll({
    el: document.querySelector('[data-scroll-container]'),
    smooth: true,
    smoothMobile: false,
    inertia: 0.5,
    getDirection: true,
    reloadOnContextChange: true,
    lerp: 0.08, // Linear Interpolation, 0 = no smooth scrolling, 1 = instant (no interpolation)
    multiplier: 1.0, // Scroll speed multiplier
    touchMultiplier: 2.5,
    scrollFromAnywhere: true,
    resetNativeScroll: true,
    smartphone: {
      smooth: false
    },
    tablet: {
      smooth: true,
      breakpoint: 1024
    }
  });

  // Fix for hero video - ensure it's properly loaded
  const heroVideo = document.querySelector('.hero-video');
  if (heroVideo) {
    heroVideo.addEventListener('loadeddata', () => {
      scroll.update();
    });
  }

  // Update scroll on window resize
  window.addEventListener('resize', () => {
    setTimeout(() => {
      scroll.update();
    }, 500);
  });

  // Update scroll when images and other resources are loaded
  window.addEventListener('load', () => {
    scroll.update();
  });

  // Handle navigation links
  const navLinks = document.querySelectorAll('.nav-links a, .mobile-menu a');
  navLinks.forEach(link => {
    link.addEventListener('click', function(e) {
      e.preventDefault();
      const targetId = this.getAttribute('href');

      // If it's a hash link, scroll to the section
      if (targetId.startsWith('#')) {
        const targetSection = document.querySelector(targetId);
        if (targetSection) {
          scroll.scrollTo(targetSection);

          // Close mobile menu if open
          const mobileMenu = document.querySelector('.mobile-menu');
          if (mobileMenu.classList.contains('active')) {
            mobileMenu.classList.remove('active');
          }
        }
      } else {
        // If it's a regular link, navigate to the page
        window.location.href = targetId;
      }
    });
  });

  // Add scroll animations
  const fadeElements = document.querySelectorAll('.product-card, .feature-item, .video-item');
  fadeElements.forEach((element, index) => {
    element.setAttribute('data-scroll', '');
    element.setAttribute('data-scroll-speed', '0.1');
    element.setAttribute('data-scroll-delay', (0.05 * (index % 3)).toString());
  });

  // Add fade-in effect to sections
  const sections = document.querySelectorAll('[data-scroll-section]');
  sections.forEach(section => {
    // Add fade-in class to sections
    section.classList.add('fade-section');
    section.setAttribute('data-scroll', '');
    section.setAttribute('data-scroll-offset', '100');

    // Add reveal class to section content
    const sectionContent = section.querySelector('.welcome-content, .video-container, .product-grid, .cta-content, .form-container, .footer-container');
    if (sectionContent) {
      sectionContent.classList.add('reveal-content');
      sectionContent.setAttribute('data-scroll', '');
      sectionContent.setAttribute('data-scroll-offset', '50');
    }
  });

  // Add parallax effect to hero section
  const heroTitle = document.querySelector('.hero-title');
  if (heroTitle) {
    heroTitle.setAttribute('data-scroll', '');
    heroTitle.setAttribute('data-scroll-speed', '1');
  }

  // Add fade-in effect to hero overlay content only (not the entire overlay)
  const heroOverlayContent = document.querySelector('.hero-overlay h1, .hero-overlay p');
  if (heroOverlayContent) {
    heroOverlayContent.classList.add('hero-content-fade-in');
  }

  // Remove fade-in from hero video section to prevent issues with video playback
  // Instead, we'll handle this with CSS directly

  // Add reveal animations to section titles
  const sectionTitles = document.querySelectorAll('.section-title');
  sectionTitles.forEach(title => {
    title.setAttribute('data-scroll', '');
    title.setAttribute('data-scroll-offset', '30');
  });

  // Add reveal animations to CTA elements
  const ctaElements = document.querySelectorAll('.cta-text, .cta-subheadline, .cta-buttons-centered');
  ctaElements.forEach((element, index) => {
    element.setAttribute('data-scroll', '');
    element.setAttribute('data-scroll-speed', '0.3');
    element.setAttribute('data-scroll-delay', (index * 0.1).toString());
  });

  // Add custom class when scrolling
  scroll.on('scroll', (instance) => {
    const scrollTop = instance.scroll.y;
    const navbar = document.querySelector('.navbar');

    if (scrollTop > 50) {
      navbar.classList.add('scrolled');
    } else {
      navbar.classList.remove('scrolled');
    }
  });
});
