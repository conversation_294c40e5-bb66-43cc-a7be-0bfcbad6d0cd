{"name": "@types/bonjour", "version": "3.5.13", "description": "TypeScript definitions for bonjour", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/bonjour", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "quentin-ol", "url": "https://github.com/quentin-ol"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/bonjour"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "af953fb9d89b2e08b510c2d99252988a590b758e2e636fafadf9496dee4f2b68", "typeScriptVersion": "4.5"}