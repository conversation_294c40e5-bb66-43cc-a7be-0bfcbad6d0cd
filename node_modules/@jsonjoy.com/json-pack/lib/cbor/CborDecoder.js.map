{"version": 3, "file": "CborDecoder.js", "sourceRoot": "", "sources": ["../../src/cbor/CborDecoder.ts"], "names": [], "mappings": ";;;AACA,uDAAkD;AAClD,oDAA+C;AAI/C,MAAa,WAEX,SAAQ,iCAAkB;IAGnB,SAAS;QACd,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC;QAC/B,MAAM,KAAK,GAAG,KAAK,IAAI,CAAC,CAAC;QACzB,MAAM,KAAK,GAAG,KAAK,KAAmB,CAAC;QACvC,QAAQ,KAAK,EAAE,CAAC;YACd;gBACE,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YAC7B;gBACE,QAA6B;QACjC,CAAC;IACH,CAAC;IAEM,OAAO,CAAC,KAAa;QAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QACxC,IAAI,MAAM,IAAI,CAAC;YAAE,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;;YAC3C,OAAO,IAAI,CAAC,YAAY,EAAE,CAAC;IAClC,CAAC;IAEM,UAAU,CAAC,MAAc;QAC9B,MAAM,GAAG,GAA0B,IAAI,GAAG,EAAE,CAAC;QAC7C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAChC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YACvB,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YACzB,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACtB,CAAC;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAEM,YAAY;QACjB,MAAM,GAAG,GAA0B,IAAI,GAAG,EAAE,CAAC;QAC7C,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,QAAc,EAAE,CAAC;YACxC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YACvB,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,QAAc;gBAAE,QAAiC;YACvE,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YACzB,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACtB,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;QAChB,OAAO,GAAG,CAAC;IACb,CAAC;IAIM,KAAK,CAAC,CAAS;QACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;YAAE,IAAI,CAAC,OAAO,EAAE,CAAC;IAC7C,CAAC;IACM,OAAO;QACZ,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;IACpC,CAAC;IAEM,UAAU,CAAC,KAAa;QAC7B,MAAM,KAAK,GAAG,KAAK,IAAI,CAAC,CAAC;QACzB,MAAM,KAAK,GAAG,KAAK,KAAmB,CAAC;QACvC,QAAQ,KAAK,EAAE,CAAC;YACd,OAAe;YACf;gBACE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBACtB,MAAM;YACR;gBACE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;gBACpB,MAAM;YACR;gBACE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;gBACpB,MAAM;YACR;gBACE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;gBACpB,MAAM;YACR;gBACE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;gBACpB,MAAM;YACR;gBACE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;gBACpB,MAAM;YACR;gBACE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;gBACpB,MAAM;QACV,CAAC;IACH,CAAC;IAEM,YAAY,CAAC,KAAa;QAC/B,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO,KAAK,CAAC;QAC9B,QAAQ,KAAK,EAAE,CAAC;YACd,KAAK,EAAE;gBACL,OAAO,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC;YAC1B,KAAK,EAAE;gBACL,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;YAC3B,KAAK,EAAE;gBACL,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;YAC3B,KAAK,EAAE;gBACL,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC;YACnC,KAAK,EAAE;gBACL,OAAO,CAAC,CAAC,CAAC;YACZ;gBACE,QAA6B;QACjC,CAAC;IACH,CAAC;IAIM,SAAS,CAAC,KAAa;QAC5B,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO;QACxB,QAAQ,KAAK,EAAE,CAAC;YACd,KAAK,EAAE;gBACL,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC7B,KAAK,EAAE;gBACL,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC7B,KAAK,EAAE;gBACL,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC7B,KAAK,EAAE;gBACL,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC7B;gBACE,QAA6B;QACjC,CAAC;IACH,CAAC;IAIM,OAAO,CAAC,KAAa;QAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QACxC,IAAI,MAAM,IAAI,CAAC;YAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aACrC,CAAC;YACJ,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,QAAc;gBAAE,IAAI,CAAC,YAAY,EAAE,CAAC;YAC7D,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;QAClB,CAAC;IACH,CAAC;IAEM,YAAY;QACjB,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC;QAC/B,MAAM,KAAK,GAAG,KAAK,IAAI,CAAC,CAAC;QACzB,MAAM,KAAK,GAAG,KAAK,KAAmB,CAAC;QACvC,IAAI,KAAK,MAAc;YAAE,QAAuC;QAChE,IAAI,KAAK,GAAG,EAAE;YAAE,QAAuC;QACvD,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IACtB,CAAC;IAIM,OAAO,CAAC,KAAa;QAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QACxC,IAAI,MAAM,IAAI,CAAC;YAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aACrC,CAAC;YACJ,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,QAAc;gBAAE,IAAI,CAAC,YAAY,EAAE,CAAC;YAC7D,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;QAClB,CAAC;IACH,CAAC;IAEM,YAAY;QACjB,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC;QAC/B,MAAM,KAAK,GAAG,KAAK,IAAI,CAAC,CAAC;QACzB,MAAM,KAAK,GAAG,KAAK,KAAmB,CAAC;QACvC,IAAI,KAAK,MAAc;YAAE,QAAuC;QAChE,IAAI,KAAK,GAAG,EAAE;YAAE,QAAuC;QACvD,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IACtB,CAAC;IAIM,OAAO,CAAC,KAAa;QAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QACxC,IAAI,MAAM,IAAI,CAAC;YAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;aAC/B,CAAC;YACJ,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,QAAc;gBAAE,IAAI,CAAC,OAAO,EAAE,CAAC;YACxD,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;QAClB,CAAC;IACH,CAAC;IAIM,OAAO,CAAC,KAAa;QAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QACxC,IAAI,MAAM,IAAI,CAAC;YAAE,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;aAC1C,CAAC;YACJ,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,QAAc,EAAE,CAAC;gBACxC,IAAI,CAAC,OAAO,EAAE,CAAC;gBACf,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,QAAc;oBAAE,QAAiC;gBACvE,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,CAAC;YACD,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;QAClB,CAAC;IACH,CAAC;IAIM,OAAO,CAAC,KAAa;QAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QACxC,IAAI,MAAM,GAAG,CAAC;YAAE,QAA6B;QAC7C,IAAI,CAAC,OAAO,EAAE,CAAC;IACjB,CAAC;IAIM,OAAO,CAAC,KAAa;QAC1B,QAAQ,KAAK,EAAE,CAAC;YACd,KAAK,IAAI,KAAmB;gBAC1B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBACpB,OAAO;YACT,KAAK,IAAI,KAAmB;gBAC1B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBACpB,OAAO;YACT,KAAK,IAAI,KAAmB;gBAC1B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBACpB,OAAO;YACT,KAAK,IAAI,KAAmB;gBAC1B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBACpB,OAAO;QACX,CAAC;QACD,IAAI,KAAK,IAAI,EAAE;YAAE,OAAO;QACxB,QAA6B;IAC/B,CAAC;IAiBM,QAAQ,CAAC,KAAiB,EAAE,SAAiB,CAAC,EAAE,OAAe,KAAK,CAAC,MAAM;QAChF,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACzB,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC;QACvB,MAAM,KAAK,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,OAAO,EAAE,CAAC;QACf,MAAM,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;QAC1B,IAAI,GAAG,GAAG,KAAK,KAAK,IAAI;YAAE,QAAyB;IACrD,CAAC;IAIM,WAAW,CAAC,KAAiB;QAClC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACzB,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC;IAC1B,CAAC;IAQM,SAAS;QACd,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC;QAC/B,MAAM,KAAK,GAAG,KAAK,IAAI,CAAC,CAAC;QACzB,MAAM,KAAK,GAAG,KAAK,KAAmB,CAAC;QACvC,QAAQ,KAAK,EAAE,CAAC;YACd;gBACE,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAClC;gBACE,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAClC;gBACE,OAAO,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;QACnC,CAAC;IACH,CAAC;IAOM,kBAAkB;QACvB,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;QACjC,MAAM,KAAK,GAAG,KAAK,IAAI,CAAC,CAAC;QACzB,QAAQ,KAAK,EAAE,CAAC;YACd,OAAe;YACf;gBACE,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC;YAC5B;gBACE,OAAO,IAAI,CAAC,GAAG,EAAE,CAAC;QACtB,CAAC;IACH,CAAC;IAEM,WAAW;QAChB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC;QACvB,IAAI,CAAC,OAAO,EAAE,CAAC;QACf,MAAM,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC;QACrB,OAAO,IAAI,6BAAa,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC;IAC9D,CAAC;IAIM,YAAY,CAAC,KAAa;QAC/B,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QACxC,IAAI,MAAM,IAAI,CAAC;YAAE,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;;YAChD,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;IACvC,CAAC;IAEM,eAAe,CAAC,MAAc;QACnC,MAAM,GAAG,GAA4B,EAAE,CAAC;QACxC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAChC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YACvB,MAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACxC,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACnB,CAAC;QACD,OAAO,GAAG,CAAC;IACb,CAAC;IAEM,iBAAiB;QACtB,MAAM,GAAG,GAA4B,EAAE,CAAC;QACxC,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,QAAc,EAAE,CAAC;YACxC,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YACvB,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,QAAc;gBAAE,QAAiC;YACvE,MAAM,KAAK,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACxC,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;QACnB,CAAC;QACD,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;QAChB,OAAO,GAAG,CAAC;IACb,CAAC;IAIM,YAAY,CAAC,KAAa;QAC/B,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QACxC,IAAI,MAAM,IAAI,CAAC;YAAE,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QACrD,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAClC,CAAC;IAEM,eAAe,CAAC,MAAc;QACnC,MAAM,GAAG,GAAc,EAAE,CAAC;QAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE;YAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;QACrE,OAAO,GAAG,CAAC;IACb,CAAC;IAEM,iBAAiB;QACtB,MAAM,GAAG,GAAc,EAAE,CAAC;QAC1B,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,QAAc;YAAE,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC;QAC7E,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC;QAChB,OAAO,GAAG,CAAC;IACb,CAAC;IAIM,OAAO,CAAC,aAAqB;QAClC,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC;QAC/B,MAAM,KAAK,GAAG,KAAK,IAAI,CAAC,CAAC;QACzB,IAAI,KAAK,KAAK,aAAa;YAAE,QAA6B;QAC1D,MAAM,KAAK,GAAG,KAAK,KAAmB,CAAC;QACvC,IAAI,KAAK,GAAG,EAAE;YAAE,OAAO,KAAK,CAAC;QAC7B,QAAQ,KAAK,EAAE,CAAC;YACd,KAAK,EAAE;gBACL,OAAO,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC;YAC1B,KAAK,EAAE;gBACL,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;YAC3B,KAAK,EAAE;gBACL,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;YAC3B,KAAK,EAAE;gBACL,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC;YACnC,KAAK,EAAE;gBACL,OAAO,CAAC,CAAC,CAAC;QACd,CAAC;QACD,QAA6B;IAC/B,CAAC;IAEM,UAAU;QACf,OAAO,IAAI,CAAC,OAAO,GAAW,CAAC;IACjC,CAAC;IAEM,UAAU;QACf,OAAO,IAAI,CAAC,OAAO,GAAW,CAAC;IACjC,CAAC;IAEM,UAAU;QACf,OAAO,IAAI,CAAC,OAAO,GAAW,CAAC;IACjC,CAAC;IAEM,OAAO,CAAC,GAAW;QACxB,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QAC/B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC;YAC9B,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YACrB,IAAI,CAAC,KAAK,GAAG;gBAAE,OAAO,IAAI,CAAC;YAC3B,IAAI,CAAC,OAAO,EAAE,CAAC;QACjB,CAAC;QACD,QAA0B;IAC5B,CAAC;IAEM,SAAS,CAAC,KAAa;QAC5B,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;QAC/B,IAAI,KAAK,IAAI,IAAI;YAAE,SAAgC;QACnD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE;YAAE,IAAI,CAAC,OAAO,EAAE,CAAC;QAC/C,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,IAAI,CAAC,IAAU;QACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACrC,MAAM,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACxB,IAAI,OAAO,OAAO,KAAK,QAAQ;gBAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;;gBAClD,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAC/B,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AAjZD,kCAiZC"}