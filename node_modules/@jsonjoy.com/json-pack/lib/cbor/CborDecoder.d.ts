import { CborDecoderBase } from './CborDecoderBase';
import { JsonPackValue } from '../JsonPackValue';
import type { Path } from '../json-pointer';
import type { IReader, IReaderResettable } from '@jsonjoy.com/util/lib/buffers';
export declare class CborDecoder<R extends IReader & IReaderResettable = IReader & IReaderResettable> extends CborDecoderBase<R> {
    readAsMap(): Map<unknown, unknown>;
    readMap(minor: number): Map<unknown, unknown>;
    readMapRaw(length: number): Map<unknown, unknown>;
    readMapIndef(): Map<unknown, unknown>;
    skipN(n: number): void;
    skipAny(): void;
    skipAnyRaw(octet: number): void;
    skipMinorLen(minor: number): number;
    skipUNint(minor: number): void;
    skipBin(minor: number): void;
    skipBinChunk(): void;
    skipStr(minor: number): void;
    skipStrChunk(): void;
    skipArr(minor: number): void;
    skipObj(minor: number): void;
    skipTag(minor: number): void;
    skipTkn(minor: number): void;
    validate(value: Uint8Array, offset?: number, size?: number): void;
    decodeLevel(value: Uint8Array): unknown;
    readLevel(): unknown;
    readPrimitiveOrVal(): unknown | JsonPackValue;
    readAsValue(): JsonPackValue;
    readObjLevel(minor: number): Record<string, unknown>;
    readObjRawLevel(length: number): Record<string, unknown>;
    readObjIndefLevel(): Record<string, unknown>;
    readArrLevel(minor: number): unknown[];
    readArrRawLevel(length: number): unknown[];
    readArrIndefLevel(): unknown[];
    readHdr(expectedMajor: number): number;
    readStrHdr(): number;
    readObjHdr(): number;
    readArrHdr(): number;
    findKey(key: string): this;
    findIndex(index: number): this;
    find(path: Path): this;
}
