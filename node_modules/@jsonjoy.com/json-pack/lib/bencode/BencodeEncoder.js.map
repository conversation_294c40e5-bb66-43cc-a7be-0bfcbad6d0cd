{"version": 3, "file": "BencodeEncoder.js", "sourceRoot": "", "sources": ["../../src/bencode/BencodeEncoder.ts"], "names": [], "mappings": ";;;AAAA,6DAA4D;AAC5D,oEAA0D;AAI1D,MAAa,cAAc;IACzB,YAA4B,MAAiC;QAAjC,WAAM,GAAN,MAAM,CAA2B;IAAG,CAAC;IAE1D,MAAM,CAAC,KAAc;QAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,CAAC,KAAK,EAAE,CAAC;QACf,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QACrB,OAAO,MAAM,CAAC,KAAK,EAAE,CAAC;IACxB,CAAC;IAOM,YAAY,CAAC,KAAc;QAChC,IAAI,CAAC,SAAS,EAAE,CAAC;IACnB,CAAC;IAEM,QAAQ,CAAC,KAAc;QAC5B,QAAQ,OAAO,KAAK,EAAE,CAAC;YACrB,KAAK,SAAS;gBACZ,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAClC,KAAK,QAAQ;gBACX,OAAO,IAAI,CAAC,WAAW,CAAC,KAAe,CAAC,CAAC;YAC3C,KAAK,QAAQ;gBACX,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAC9B,KAAK,QAAQ,CAAC,CAAC,CAAC;gBACd,IAAI,KAAK,KAAK,IAAI;oBAAE,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC;gBAC5C,MAAM,WAAW,GAAG,KAAK,CAAC,WAAW,CAAC;gBACtC,QAAQ,WAAW,EAAE,CAAC;oBACpB,KAAK,MAAM;wBACT,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAgC,CAAC,CAAC;oBACzD,KAAK,KAAK;wBACR,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAkB,CAAC,CAAC;oBAC3C,KAAK,UAAU;wBACb,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAmB,CAAC,CAAC;oBAC5C,KAAK,GAAG;wBACN,OAAO,IAAI,CAAC,QAAQ,CAAC,KAA8B,CAAC,CAAC;oBACvD,KAAK,GAAG;wBACN,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAqB,CAAC,CAAC;oBAC9C;wBACE,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;gBACpC,CAAC;YACH,CAAC;YACD,KAAK,QAAQ,CAAC,CAAC,CAAC;gBACd,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YACjC,CAAC;YACD,KAAK,WAAW,CAAC,CAAC,CAAC;gBACjB,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC;YAC3B,CAAC;YACD;gBACE,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QACpC,CAAC;IACH,CAAC;IAEM,SAAS;QACd,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;IACtB,CAAC;IAEM,UAAU;QACf,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;IACtB,CAAC;IAEM,YAAY,CAAC,IAAa;QAC/B,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IACrC,CAAC;IAEM,WAAW,CAAC,GAAW;QAC5B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;QAChB,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC;QACnC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;IAClB,CAAC;IAEM,YAAY,CAAC,GAAW;QAC7B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;QAChB,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC;QACvB,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;IAClB,CAAC;IAEM,aAAa,CAAC,IAAY;QAC/B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IAEM,UAAU,CAAC,KAAa;QAC7B,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;IAC1B,CAAC;IAEM,WAAW,CAAC,GAAW;QAC5B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;QAChB,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC;QACvB,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;IAClB,CAAC;IAEM,QAAQ,CAAC,GAAe;QAC7B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;QAC1B,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC;QAC1B,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;QAChB,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;IAC1B,CAAC;IAEM,QAAQ,CAAC,GAAW;QACzB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,MAAM,GAAG,IAAA,eAAQ,EAAC,GAAG,CAAC,CAAC;QAC7B,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC;QAC1B,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;QAChB,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QAC9B,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACnB,CAAC;IAEM,aAAa,CAAC,GAAW;QAC9B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC;QAC9B,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;QAChB,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACpB,CAAC;IAEM,QAAQ,CAAC,GAAc;QAC5B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;QAChB,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;QAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE;YAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACvD,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;IAClB,CAAC;IAEM,QAAQ,CAAC,GAA4B;QAC1C,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;QAChB,MAAM,IAAI,GAAG,IAAA,gBAAI,EAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;QACpC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAChC,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACpB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YACnB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAC1B,CAAC;QACD,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;IAClB,CAAC;IAEM,QAAQ,CAAC,GAA0B;QACxC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;QAChB,MAAM,IAAI,GAAG,IAAA,gBAAI,EAAC,CAAC,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QACnC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;QAC3B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAChC,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACpB,IAAI,CAAC,QAAQ,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC;YACxB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QAC9B,CAAC;QACD,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;IAClB,CAAC;IAEM,QAAQ,CAAC,GAAiB;QAC/B,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IACnC,CAAC;CACF;AA9JD,wCA8JC"}