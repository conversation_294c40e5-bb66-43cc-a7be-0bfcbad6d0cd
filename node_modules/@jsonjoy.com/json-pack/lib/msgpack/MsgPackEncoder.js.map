{"version": 3, "file": "MsgPackEncoder.js", "sourceRoot": "", "sources": ["../../src/msgpack/MsgPackEncoder.ts"], "names": [], "mappings": ";;;AAAA,6DAAwD;AACxD,6EAAwE;AACxE,4DAAuD;AACvD,oDAA+C;AAO/C,MAAa,cAEX,SAAQ,uCAAqB;IACtB,QAAQ,CAAC,KAAc;QAC5B,QAAQ,KAAK,EAAE,CAAC;YACd,KAAK,IAAI;gBACP,OAAO,IAAI,CAAC,MAAM,CAAC,EAAE,KAAc,CAAC;YACtC,KAAK,KAAK;gBACR,OAAO,IAAI,CAAC,MAAM,CAAC,EAAE,KAAe,CAAC;YACvC,KAAK,IAAI;gBACP,OAAO,IAAI,CAAC,MAAM,CAAC,EAAE,KAAc,CAAC;QACxC,CAAC;QACD,IAAI,KAAK,YAAY,KAAK;YAAE,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAC3D,QAAQ,OAAO,KAAK,EAAE,CAAC;YACrB,KAAK,QAAQ;gBACX,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAClC,KAAK,QAAQ;gBACX,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAClC,KAAK,QAAQ,CAAC,CAAC,CAAC;gBACd,IAAI,KAAK,YAAY,6BAAa;oBAAE,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;gBACxF,IAAI,KAAK,YAAY,qCAAiB;oBAAE,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBACrE,IAAI,IAAA,2BAAY,EAAC,KAAK,CAAC;oBAAE,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;gBACzD,OAAO,IAAI,CAAC,YAAY,CAAC,KAAgC,CAAC,CAAC;YAC7D,CAAC;YACD,KAAK,WAAW;gBACd,OAAO,IAAI,CAAC,MAAM,CAAC,EAAE,KAAmB,CAAC;QAC7C,CAAC;IACH,CAAC;CACF;AA5BD,wCA4BC"}