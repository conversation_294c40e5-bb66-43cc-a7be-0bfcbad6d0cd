{"version": 3, "file": "asString.js", "sourceRoot": "", "sources": ["../../src/strings/asString.ts"], "names": [], "mappings": ";;;AAAA,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;AAG1B,MAAM,QAAQ,GAAG,CAAC,GAAW,EAAE,EAAE;IACtC,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;IAC1B,IAAI,MAAM,GAAG,EAAE;QAAE,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC;IACvC,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,IAAI,IAAI,GAAG,CAAC,CAAC;IACb,IAAI,KAAK,GAAG,KAAK,CAAC;IAClB,IAAI,KAAK,GAAG,GAAG,CAAC;IAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,IAAI,KAAK,IAAI,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;QAC/C,KAAK,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QAC1B,IAAI,KAAK,IAAI,MAAM,IAAI,KAAK,IAAI,MAAM;YAAE,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC;QAC9D,IAAI,KAAK,KAAK,EAAE,IAAI,KAAK,KAAK,EAAE,EAAE,CAAC;YACjC,MAAM,IAAI,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC;YACpC,IAAI,GAAG,CAAC,CAAC;YACT,KAAK,GAAG,IAAI,CAAC;QACf,CAAC;IACH,CAAC;IACD,IAAI,KAAK,GAAG,EAAE;QAAE,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC;IACtC,OAAO,GAAG,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC;AAC/D,CAAC,CAAC;AAlBW,QAAA,QAAQ,YAkBnB"}