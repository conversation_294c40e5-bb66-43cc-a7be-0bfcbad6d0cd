{"version": 3, "file": "v10.js", "sourceRoot": "", "sources": ["../../../../src/buffers/utf8/decodeUtf8/v10.ts"], "names": [], "mappings": ";;AAAA,MAAM,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC;AAEzC,kBAAe,CAAC,GAAe,EAAE,KAAa,EAAE,MAAc,EAAU,EAAE;IACxE,IAAI,MAAM,GAAG,KAAK,CAAC;IACnB,MAAM,GAAG,GAAG,MAAM,GAAG,MAAM,CAAC;IAC5B,IAAI,GAAG,GAAG,EAAE,CAAC;IACb,OAAO,MAAM,GAAG,GAAG,EAAE,CAAC;QACpB,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,EAAE,CAAE,CAAC;QAC9B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;YAC1B,GAAG,IAAI,YAAY,CAAC,MAAM,CAAC,CAAC;YAC5B,SAAS;QACX,CAAC;QACD,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,EAAE,CAAE,GAAG,IAAI,CAAC;QACrC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;YAC7B,GAAG,IAAI,YAAY,CAAC,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;YACrD,SAAS;QACX,CAAC;QACD,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,EAAE,CAAE,GAAG,IAAI,CAAC;QACrC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;YAC7B,GAAG,IAAI,YAAY,CAAC,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;YACtE,SAAS;QACX,CAAC;QACD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;YAC7B,MAAM,MAAM,GAAG,GAAG,CAAC,MAAM,EAAE,CAAE,GAAG,IAAI,CAAC;YACrC,IAAI,IAAI,GAAG,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,IAAI,IAAI,CAAC,GAAG,CAAC,MAAM,IAAI,IAAI,CAAC,GAAG,MAAM,CAAC;YACpF,IAAI,IAAI,GAAG,MAAM,EAAE,CAAC;gBAClB,IAAI,IAAI,OAAO,CAAC;gBAChB,MAAM,KAAK,GAAG,CAAC,CAAC,IAAI,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC;gBAC/C,IAAI,GAAG,MAAM,GAAG,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC;gBAC/B,GAAG,IAAI,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YACnC,CAAC;iBAAM,CAAC;gBACN,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,CAAC;YAC5B,CAAC;QACH,CAAC;aAAM,CAAC;YACN,GAAG,IAAI,YAAY,CAAC,MAAM,CAAC,CAAC;QAC9B,CAAC;IACH,CAAC;IACD,OAAO,GAAG,CAAC;AACb,CAAC,CAAC"}