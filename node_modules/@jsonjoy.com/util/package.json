{"name": "@jsonjoy.com/util", "private": false, "publishConfig": {"access": "public"}, "version": "1.6.0", "description": "Various helper utilities", "author": {"name": "<PERSON>ich", "url": "https://github.com/streamich"}, "homepage": "https://github.com/jsonjoy-com/util", "repository": "jsonjoy-com/util", "license": "Apache-2.0", "funding": {"type": "github", "url": "https://github.com/sponsors/streamich"}, "keywords": ["codegen", "buffer", "string", "utf8", "json", "json-equal", "json-brand", "json-random", "fuzzer"], "engines": {"node": ">=10.0"}, "main": "lib/index.js", "types": "lib/index.d.ts", "typings": "lib/index.d.ts", "files": ["LICENSE", "lib/"], "scripts": {"prettier": "prettier --ignore-path .gitignore --write \"src/**/*.{ts,tsx,js,jsx}\"", "prettier:check": "prettier --ignore-path .gitignore --list-different 'src/**/*.{ts,tsx,js,jsx}'", "lint": "yarn tslint", "tslint": "tslint 'src/**/*.{js,jsx,ts,tsx}' -t verbose --project .", "clean": "rimraf lib typedocs coverage gh-pages yarn-error.log", "build": "tsc --project tsconfig.build.json --module commonjs --target es2020 --outDir lib", "jest": "node -r ts-node/register ./node_modules/.bin/jest", "test": "jest --maxWorkers 7", "test:ci": "yarn jest --maxWorkers 3 --no-cache", "coverage": "yarn test --collectCoverage", "typedoc": "typedoc", "build:pages": "rimraf gh-pages && mkdir -p gh-pages && cp -r typedocs/* gh-pages && cp -r coverage gh-pages/coverage", "deploy:pages": "gh-pages -d gh-pages", "publish-coverage-and-typedocs": "yarn typedoc && yarn coverage && yarn build:pages && yarn deploy:pages"}, "peerDependencies": {"tslib": "2"}, "dependencies": {}, "devDependencies": {"@types/benchmark": "^2.1.2", "@types/jest": "^29.5.12", "benchmark": "^2.1.4", "jest": "^29.7.0", "json-pack-napi": "^0.0.2", "prettier": "^3.2.5", "pretty-quick": "^3.1.3", "rimraf": "^5.0.0", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "tslib": "^2.6.2", "tslint": "^6.1.3", "tslint-config-common": "^1.6.2", "typedoc": "^0.25.12", "typescript": "^5.4.4"}, "jest": {"verbose": true, "testEnvironmentOptions": {"url": "http://localhost/"}, "setupFiles": ["<rootDir>/src/__tests__/setup.js"], "moduleFileExtensions": ["ts", "js"], "transform": {"^.+\\.ts$": "ts-jest"}, "transformIgnorePatterns": [], "testRegex": ".*/(__tests__|__jest__|demo)/.*\\.(test|spec)\\.ts$"}, "prettier": {"arrowParens": "always", "printWidth": 120, "tabWidth": 2, "useTabs": false, "semi": true, "singleQuote": true, "trailingComma": "all", "bracketSpacing": false}}