/* Form Section Styles */
.form-section {
  padding: 8rem 0 6rem;
  min-height: calc(100vh - 80px);
  width: 100%;
}

.form-container {
  max-width: 900px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 20px;
  padding: 3rem;
  backdrop-filter: blur(10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

/* Progress Indicator */
.progress-container {
  margin-bottom: 3rem;
  text-align: center;
  padding: 1rem 0;
}

.progress-percentage {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 0.8rem;
  letter-spacing: 0.05em;
}

.progress-bar {
  height: 10px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 5px;
  overflow: hidden;
  position: relative;
  max-width: 80%;
  margin: 0 auto;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-fill {
  height: 100%;
  background-color: var(--primary-color);
  background-image: linear-gradient(to right, #ff3030, #ff5050);
  border-radius: 5px;
  width: 0%;
  transition: width 0.5s ease;
  box-shadow: 0 0 10px rgba(255, 48, 48, 0.5);
}

/* Email notification */
.email-notification {
  margin-top: 1rem;
  padding: 0.8rem 1.5rem;
  background-color: rgba(46, 204, 113, 0.1);
  border-left: 3px solid #2ecc71;
  color: #2ecc71;
  border-radius: 4px;
  font-size: 0.9rem;
  display: inline-block;
  opacity: 1;
  transition: opacity 1s ease;
}

.email-notification i {
  margin-right: 0.5rem;
}



/* Multi-step Form */
.multi-step-form {
  position: relative;
}

.form-step {
  display: none;
  animation: fadeIn 0.5s ease forwards;
}

.form-step.active {
  display: block;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Form Content Styling */
.step-content {
  padding: 1rem;
}

.form-title {
  font-size: 2.8rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  line-height: 1.2;
  text-align: center;
  color: var(--text-light);
}

.form-subtitle {
  font-size: 1.4rem;
  color: var(--text-gray);
  margin-bottom: 2.5rem;
  text-align: center;
  max-width: 80%;
  margin-left: auto;
  margin-right: auto;
}

.step-title {
  font-size: 1.8rem;
  font-weight: 600;
  margin-bottom: 2rem;
  color: var(--text-light);
  position: relative;
  padding-bottom: 1rem;
}

.step-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background-color: var(--primary-color);
  border-radius: 1.5px;
}

.step-description {
  font-size: 1.1rem;
  color: var(--text-gray);
  margin-bottom: 2rem;
  line-height: 1.5;
}

.form-image {
  margin: 2rem auto;
  max-width: 500px;
  border-radius: 15px;
  overflow: hidden;
}

.form-image img {
  width: 100%;
  height: auto;
  display: block;
}

/* Form Controls */
.form-group {
  margin-bottom: 2rem;
}

.form-group label {
  display: block;
  font-size: 1.1rem;
  font-weight: 500;
  margin-bottom: 1rem;
  color: var(--text-light);
}

.form-group input[type="text"],
.form-group input[type="email"],
.form-group input[type="tel"],
.form-group textarea {
  width: 100%;
  padding: 1rem;
  background-color: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: var(--text-light);
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-group input[type="text"]:focus,
.form-group input[type="email"]:focus,
.form-group input[type="tel"]:focus,
.form-group textarea:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 2px rgba(255, 48, 48, 0.2);
}

.checkbox-group,
.radio-group {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.checkbox-item,
.radio-item {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  padding: 1rem 1.2rem;
  background-color: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.05);
  position: relative;
  overflow: hidden;
}

.checkbox-item:hover,
.radio-item:hover {
  background-color: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 48, 48, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
}

.radio-item input[type="radio"]:checked + label {
  color: var(--primary-color);
  font-weight: 500;
}

.radio-item input[type="radio"]:checked ~ .radio-item {
  background-color: rgba(255, 48, 48, 0.05);
  border-color: var(--primary-color);
}

.checkbox-item label,
.radio-item label {
  margin-bottom: 0;
  font-weight: 400;
  cursor: pointer;
  flex: 1;
}

.checkbox-item input[type="checkbox"],
.radio-item input[type="radio"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
  accent-color: var(--primary-color);
}

.time-slot-morning, .time-slot-afternoon, .time-slot-evening {
  margin-bottom: 2rem;
  background-color: rgba(255, 255, 255, 0.02);
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.time-slot-morning h4, .time-slot-afternoon h4, .time-slot-evening h4 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1.2rem;
  color: var(--primary-color);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 0.8rem;
}

.time-slots {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}

@media (max-width: 768px) {
  .time-slots {
    grid-template-columns: 1fr;
  }
}

.confirmation-details {
  background-color: rgba(255, 255, 255, 0.03);
  border-radius: 10px;
  padding: 1.5rem;
  margin: 2rem 0;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.confirmation-details p {
  margin-bottom: 1rem;
  color: var(--text-light);
}

.confirmation-details p:last-child {
  margin-bottom: 0;
}

.confirmation-details strong {
  color: var(--primary-color);
  margin-right: 0.5rem;
}

.error-message {
  color: var(--primary-color);
  margin-top: 0.5rem;
  font-size: 0.9rem;
  animation: fadeIn 0.3s ease;
}

.inline-input {
  width: auto !important;
  margin-left: 0.5rem;
  display: inline-block !important;
  max-width: 200px;
}

/* Form Buttons */
.form-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 3rem;
}

.form-buttons.text-center {
  justify-content: center;
}

.form-buttons.text-center .btn {
  padding: 1.2rem 3rem;
  font-size: 1.2rem;
  min-width: 250px;
}

/* Completion Message */
.completion-message {
  text-align: center;
  margin: 2rem 0;
}

.completion-message p {
  margin-bottom: 1rem;
  font-size: 1.1rem;
  color: var(--text-gray);
}

.completion-message .highlight {
  color: var(--primary-color);
  font-weight: 500;
  font-size: 1.2rem;
}

/* Responsive Styles */
@media (max-width: 992px) {
  .form-container {
    padding: 2rem;
  }

  .form-title {
    font-size: 2.4rem;
  }

  .form-subtitle {
    font-size: 1.2rem;
    max-width: 90%;
  }

  .step-label {
    display: none;
  }
}

@media (max-width: 768px) {
  .form-section {
    padding: 6rem 0 4rem;
  }

  .form-container {
    padding: 1.5rem;
  }

  .form-steps {
    margin-bottom: 2rem;
  }

  .step-number {
    width: 30px;
    height: 30px;
    font-size: 0.9rem;
  }

  .form-title {
    font-size: 2rem;
  }

  .form-subtitle {
    font-size: 1.1rem;
  }

  .step-title {
    font-size: 1.5rem;
  }

  .form-buttons {
    flex-direction: column;
    gap: 1rem;
  }

  .form-buttons button {
    width: 100%;
  }
}
