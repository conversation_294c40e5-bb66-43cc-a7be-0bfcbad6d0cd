/* Locomotive Scroll Styles */
html.has-scroll-smooth {
  overflow: hidden;
}

html.has-scroll-dragging {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.has-scroll-smooth body {
  overflow: hidden;
}

.has-scroll-smooth [data-scroll-container] {
  min-height: 100vh;
}

[data-scroll-direction="horizontal"] [data-scroll-container] {
  height: 100vh;
  display: inline-block;
  white-space: nowrap;
}

[data-scroll-direction="horizontal"] [data-scroll-section] {
  display: inline-block;
  vertical-align: top;
  white-space: nowrap;
  height: 100%;
}

.c-scrollbar {
  position: absolute;
  right: 0;
  top: 0;
  width: 11px;
  height: 100%;
  transform-origin: center right;
  transition: transform 0.3s, opacity 0.3s;
  opacity: 0;
}

.c-scrollbar:hover {
  transform: scaleX(1.45);
}

.c-scrollbar_thumb {
  position: absolute;
  top: 0;
  right: 0;
  background-color: var(--primary-color);
  opacity: 0.5;
  width: 7px;
  border-radius: 10px;
  margin: 2px;
  cursor: grab;
}

.has-scroll-dragging .c-scrollbar_thumb {
  cursor: grabbing;
}

.has-scroll-scrolling .c-scrollbar {
  opacity: 1;
}

/* Animation classes for Locomotive Scroll */
[data-scroll] {
  transition: opacity 1s, transform 1s;
}

[data-scroll="in"] {
  opacity: 1;
  transform: translateY(0);
}

[data-scroll="out"] {
  opacity: 0;
  transform: translateY(100px);
}

/* Navbar transition with scroll */
.navbar {
  transition: background-color 0.3s ease, transform 0.4s ease;
}

.navbar.scrolled {
  background-color: rgba(18, 18, 18, 0.95);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
}

/* Fade animations for sections */
.fade-section {
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 1.2s ease-out, transform 1.2s ease-out;
  will-change: opacity, transform;
}

.fade-section[data-scroll="in"] {
  opacity: 1;
  transform: translateY(0);
}

.fade-section[data-scroll="out"] {
  opacity: 0;
  transform: translateY(30px);
}

/* Reveal content animations */
.reveal-content {
  opacity: 0;
  transform: translateY(40px);
  transition: opacity 1s ease-out, transform 1s ease-out;
  transition-delay: 0.2s;
  will-change: opacity, transform;
}

.reveal-content[data-scroll="in"] {
  opacity: 1;
  transform: translateY(0);
}

.reveal-content[data-scroll="out"] {
  opacity: 0;
  transform: translateY(40px);
}

/* Parallax effects */
[data-scroll-speed] {
  will-change: transform;
}

/* Stagger animations for multiple elements */
[data-scroll-delay] {
  transition-delay: attr(data-scroll-delay);
}

/* Custom animations for specific elements */
.hero-title[data-scroll] {
  transition: transform 1.5s cubic-bezier(0.25, 1, 0.5, 1);
}

.section-title[data-scroll] {
  transition: transform 1s cubic-bezier(0.25, 1, 0.5, 1), opacity 1s ease;
}

.cta-text[data-scroll],
.cta-subheadline[data-scroll],
.cta-buttons-centered[data-scroll] {
  transition: transform 0.8s cubic-bezier(0.25, 1, 0.5, 1), opacity 0.8s ease;
}

/* Add 2px space between subheadline and button in CTA */
.cta-subheadline {
  margin-bottom: 2px;
}

/* Additional subtle animations for specific elements */
.section-title {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.8s ease, transform 0.8s ease;
}

.section-title[data-scroll="in"] {
  opacity: 1;
  transform: translateY(0);
}

.product-card, .feature-item, .video-item {
  opacity: 0;
  transform: translateY(25px);
  transition: opacity 0.7s ease, transform 0.7s ease;
}

.product-card[data-scroll="in"],
.feature-item[data-scroll="in"],
.video-item[data-scroll="in"] {
  opacity: 1;
  transform: translateY(0);
}

/* Hero section fade-in animations - fixed for video compatibility */
.hero-overlay h1 {
  animation: fadeInUp 1.2s ease-out forwards;
  animation-delay: 0.3s;
  opacity: 0;
  transform: translateY(20px);
}

.hero-overlay p {
  animation: fadeInUp 1.2s ease-out forwards;
  animation-delay: 0.6s;
  opacity: 0;
  transform: translateY(20px);
}

.hero-content-fade-in {
  animation: fadeIn 1.5s ease forwards;
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Staggered animations for grid items */
.product-card:nth-child(2),
.feature-item:nth-child(2),
.video-item:nth-child(2) {
  transition-delay: 0.1s;
}

.product-card:nth-child(3),
.feature-item:nth-child(3),
.video-item:nth-child(3) {
  transition-delay: 0.2s;
}

.product-card:nth-child(4),
.feature-item:nth-child(4) {
  transition-delay: 0.3s;
}

.product-card:nth-child(5) {
  transition-delay: 0.4s;
}

.product-card:nth-child(6) {
  transition-delay: 0.5s;
}
