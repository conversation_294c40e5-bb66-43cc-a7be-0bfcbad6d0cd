/* Portfolio Page Styles */

.portfolio-section {
  padding: 5rem 0;
}

.portfolio-intro {
  max-width: 900px;
  margin: 0 auto 3rem;
  text-align: center;
}

.portfolio-intro p {
  font-size: 1.3rem;
  line-height: 1.7;
  color: var(--text-light);
}

/* Filter Buttons */
.portfolio-filter {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 3rem;
}

.filter-btn {
  background-color: transparent;
  border: 2px solid var(--primary-color);
  color: var(--text-light);
  padding: 0.7rem 1.5rem;
  border-radius: 50px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.filter-btn:hover {
  background-color: rgba(255, 48, 48, 0.1);
  transform: translateY(-3px);
}

.filter-btn.active {
  background-color: var(--primary-color);
  color: white;
}

/* Portfolio Grid */
.portfolio-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2.5rem;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 1rem;
}

.portfolio-item {
  background-color: var(--dark-secondary);
  border-radius: 15px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 100%;
}

.portfolio-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

.portfolio-video {
  width: 100%;
  border-radius: 15px;
  overflow: hidden;
  aspect-ratio: 16 / 9;
}

/* Hide items based on filter */
.portfolio-item.hidden {
  display: none;
}

/* Responsive Styles */
@media (max-width: 1200px) {
  .portfolio-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
}

@media (max-width: 992px) {
  .portfolio-section {
    padding: 4rem 2rem;
  }

  .portfolio-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 2rem;
  }

  .portfolio-intro p {
    font-size: 1.2rem;
  }
}

@media (max-width: 768px) {
  .portfolio-section {
    padding: 3rem 1.5rem;
  }

  .portfolio-grid {
    grid-template-columns: 1fr;
    max-width: 500px;
  }

  .portfolio-filter {
    flex-direction: column;
    align-items: center;
    gap: 0.8rem;
  }

  .filter-btn {
    width: 100%;
    max-width: 250px;
  }
}

/* Plyr Video Player Customization for Portfolio */
.portfolio-video .plyr {
  border-radius: 0;
}

.portfolio-video .plyr--video {
  background-color: var(--dark-bg);
}

/* Video Poster Image */
.portfolio-video video {
  width: 100%;
  height: auto;
  display: block;
}
