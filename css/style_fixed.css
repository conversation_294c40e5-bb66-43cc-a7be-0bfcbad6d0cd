@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap');

/* Tailwind CSS */
@import 'https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css';

:root {
  --primary-color: #ff3030;
  --dark-bg: #121212;
  --dark-secondary: #1e1e1e;
  --text-light: #f5f5f5;
  --text-gray: #a0a0a0;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Roboto', sans-serif;
  background-color: var(--dark-bg);
  color: var(--text-light);
  line-height: 1.6;
  font-weight: 400;
}

/* Main content container */
.content-container {
  max-width: 1440px;
  margin: 0 auto;
  width: 100%;
  padding: 0 4rem;
}

@media (max-width: 768px) {
  .content-container {
    padding: 0 2rem;
  }
}

@media (max-width: 576px) {
  .content-container {
    padding: 0 1.5rem;
  }
}

/* WhatsApp Floating Button */
.whatsapp-float {
  position: fixed;
  bottom: 30px;
  right: 30px;
  background-color: #25D366;
  color: white;
  border-radius: 50px;
  padding: 12px 24px;
  display: flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(37, 211, 102, 0.3);
  transition: all 0.3s ease;
  z-index: 999;
}

.whatsapp-float:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(37, 211, 102, 0.4);
}

.whatsapp-float i {
  font-size: 20px;
}

@media (max-width: 768px) {
  .whatsapp-float {
    bottom: 20px;
    right: 20px;
    padding: 10px 20px;
  }
}

/* Navigation */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000;
  padding: 1rem 4rem;
  transition: background-color 0.3s ease;
  background-color: rgba(18, 18, 18, 0.8);
  backdrop-filter: blur(10px);
}

.navbar.scrolled {
  background-color: rgba(18, 18, 18, 0.95);
}

.navbar-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1440px;
  margin: 0 auto;
}

@media (max-width: 768px) {
  .navbar {
    padding: 1rem 2rem;
  }
}

@media (max-width: 576px) {
  .navbar {
    padding: 1rem 1.5rem;
  }
}

.logo img {
  height: 50px;
  filter: brightness(0) invert(1);
}

/* Pill-style navigation */
.nav-links {
  display: flex;
  gap: 0.5rem;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50px;
  padding: 0.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.nav-links a {
  color: var(--text-light);
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
  padding: 0.6rem 1.2rem;
  border-radius: 50px;
  font-size: 0.95rem;
}

.nav-links a:hover {
  color: var(--text-light);
  background-color: rgba(255, 255, 255, 0.1);
}

.nav-links a.active {
  color: #fff;
  background-color: var(--primary-color);
  box-shadow: 0 2px 4px rgba(255, 48, 48, 0.3);
}

/* Get Template button */
.get-template-btn {
  background-color: #4285f4;
  color: white;
  border: none;
  border-radius: 50px;
  padding: 0.6rem 1.2rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.95rem;
  box-shadow: 0 2px 4px rgba(66, 133, 244, 0.3);
}

.get-template-btn:hover {
  background-color: #3367d6;
  transform: translateY(-2px);
}

.get-template-btn i {
  font-size: 1rem;
}

.mobile-menu-btn {
  display: none;
  background: none;
  border: none;
  color: var(--text-light);
  font-size: 1.5rem;
  cursor: pointer;
}

/* Hero Video Section */
.hero-video-section {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  margin: 0;
  padding: 0;
  z-index: 1;
}

.hero-container {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.hero-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: rgba(0,0,0,0.4);
}

.hero-overlay h1 {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #fff;
  text-align: center;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  max-width: 80%;
}

.hero-overlay p {
  font-size: 1.5rem;
  font-weight: 400;
  color: #fff;
  text-align: center;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
  max-width: 80%;
}

/* Hero play button (fallback for autoplay) */
.hero-play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
  background-color: rgba(255, 48, 48, 0.8);
  color: white;
  border: none;
  border-radius: 50%;
  width: 80px;
  height: 80px;
  font-size: 2rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.hero-play-button:hover {
  background-color: var(--primary-color);
  transform: translate(-50%, -50%) scale(1.1);
}

@media (max-width: 1024px) {
  .hero-overlay h1 {
    font-size: 2.5rem;
    max-width: 90%;
  }
  .hero-overlay p {
    font-size: 1.2rem;
    max-width: 90%;
  }

  .hero-play-button {
    width: 70px;
    height: 70px;
    font-size: 1.8rem;
  }
}

@media (max-width: 768px) {
  .hero-video-section {
    height: 80vh;
  }
  .hero-video,
  .hero-overlay {
    height: 80vh;
  }
  .hero-overlay h1 {
    font-size: 2rem;
    max-width: 95%;
    padding: 0 1rem;
  }
  .hero-overlay p {
    font-size: 1.1rem;
    max-width: 95%;
    padding: 0 1rem;
  }

  .hero-play-button {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .hero-video-section {
    height: 70vh;
  }
  .hero-video,
  .hero-overlay {
    height: 70vh;
  }
  .hero-overlay h1 {
    font-size: 1.7rem;
    margin-bottom: 0.8rem;
  }
  .hero-overlay p {
    font-size: 1rem;
  }
}

/* Mobile Navigation */
@media (max-width: 992px) {
  .nav-links {
    display: none;
  }

  .mobile-menu-btn {
    display: block;
  }

  .mobile-menu {
    position: fixed;
    top: 80px;
    left: 0;
    width: 100%;
    background-color: rgba(18, 18, 18, 0.95);
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
    transform: translateY(-100%);
    transition: transform 0.3s ease;
    backdrop-filter: blur(10px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
    z-index: 999;
  }

  .mobile-menu.active {
    transform: translateY(0);
  }

  .mobile-menu a {
    color: var(--text-light);
    text-decoration: none;
    font-weight: 500;
    font-size: 1.1rem;
    padding: 0.8rem 1.2rem;
    border-radius: 50px;
    background-color: rgba(255, 255, 255, 0.05);
    transition: all 0.3s ease;
    text-align: center;
  }

  .mobile-menu a:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }

  .mobile-menu a.active {
    color: #fff;
    background-color: var(--primary-color);
    box-shadow: 0 2px 4px rgba(255, 48, 48, 0.3);
  }

  .get-template-mobile {
    background-color: #4285f4;
    color: white;
    border-radius: 50px;
    padding: 0.8rem 1.2rem;
    font-weight: 500;
    text-align: center;
    margin-top: 0.5rem;
    box-shadow: 0 2px 4px rgba(66, 133, 244, 0.3);
  }
}

/* Hero Section */
.welcome-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  padding: 8rem 0 6rem;
  width: 100%;
  margin: 0 auto;
  min-height: calc(100vh - 80px);
}

.welcome-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
  position: relative;
  padding-right: 2rem;
}

.hero-title {
  font-size: 3.8rem;
  font-weight: 700;
  margin-bottom: 2rem;
  line-height: 1.1;
  font-family: 'Roboto', sans-serif;
}

.welcome-subtitle {
  font-size: 1.6rem;
  color: var(--text-light);
  margin-bottom: 2.5rem;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.welcome-description {
  font-size: 1.1rem;
  color: var(--text-gray);
  line-height: 1.8;
  margin-bottom: 3rem;
  max-width: 90%;
}

.welcome-content .welcome-buttons {
  display: flex;
  gap: 1.5rem;
  margin-top: 2rem;
  justify-content: flex-start;
}

@media (max-width: 1200px) {
  .welcome-section {
    gap: 3.5rem;
    padding: 7rem 2rem 5rem;
  }

  .hero-title {
    font-size: 3.2rem;
    line-height: 1.15;
  }

  .welcome-description {
    max-width: 95%;
  }

  .welcome-features {
    padding: 2.5rem;
  }
}

@media (max-width: 992px) {
  .welcome-section {
    grid-template-columns: 1fr;
    gap: 3rem;
    padding: 6rem 2rem 4rem;
  }

  .welcome-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
    padding-right: 0;
  }

  .welcome-description {
    text-align: center;
    margin-left: auto;
    margin-right: auto;
  }

  .welcome-content .welcome-buttons {
    justify-content: center;
  }

  .welcome-features {
    padding-left: 2.5rem;
  }

  .welcome-features::before {
    left: 0;
  }

  .welcome-features h2 {
    text-align: center;
  }

  .welcome-features h2::after {
    left: 50%;
    transform: translateX(-50%);
  }
}

@media (min-width: 993px) {
  .welcome-section {
    grid-template-columns: 1fr 1fr;
    align-items: stretch;
  }

  .welcome-content {
    padding-right: 2rem;
  }
}

@media (max-width: 768px) {
  .welcome-section {
    padding: 5rem 1.5rem 4rem;
  }

  .welcome-features {
    padding: 2rem;
  }

  .features-grid {
    gap: 1.5rem;
  }

  .welcome-content .welcome-buttons {
    gap: 1.2rem;
  }

  .welcome-content .welcome-buttons .btn {
    min-width: 160px;
  }
}

@media (max-width: 576px) {
  .welcome-section {
    padding: 4rem 1.5rem 3rem;
  }

  .hero-title {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    line-height: 1.2;
  }

  .welcome-subtitle {
    font-size: 1.3rem;
    margin-bottom: 2rem;
  }

  .welcome-description {
    font-size: 1rem;
    line-height: 1.7;
  }

  .welcome-features {
    padding: 1.8rem;
  }

  .feature-item {
    padding: 1.5rem;
  }
}

/* Welcome Features */
.welcome-features {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 20px;
  padding: 3.5rem;
  backdrop-filter: blur(10px);
  display: flex;
  flex-direction: column;
  height: 100%;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.05);
  position: relative;
  padding-left: 2rem;
}

.welcome-features::before {
  content: '';
  position: absolute;
  left: 0;
  top: 10%;
  height: 80%;
  width: 4px;
  background: linear-gradient(to bottom, var(--primary-color), transparent);
  border-radius: 2px;
}

.welcome-features h2 {
  font-size: 2rem;
  margin-bottom: 2.5rem;
  color: var(--text-light);
  font-weight: 600;
  position: relative;
  padding-bottom: 1rem;
}

.welcome-features h2::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background-color: var(--primary-color);
  border-radius: 1.5px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  margin-bottom: 2.5rem;
}

.feature-item {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1.8rem;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 15px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.03);
}

.feature-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 48, 48, 0.1);
}

.feature-icon {
  font-size: 2.2rem;
  margin-bottom: 0.5rem;
}

.feature-item h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-light);
  margin-bottom: 0.5rem;
}

.feature-item p {
  color: var(--text-gray);
  line-height: 1.6;
  font-size: 1rem;
}

.welcome-cta {
  text-align: left;
  margin-top: 2rem;
}

.cta-text {
  color: var(--text-gray);
  margin-bottom: 2rem;
  font-size: 1.1rem;
  line-height: 1.6;
  text-align: left;
}

.cta-buttons {
  display: flex;
  gap: 1rem;
  justify-content: flex-start;
}

@media (max-width: 992px) {
  .welcome-features {
    max-width: 800px;
    margin: 0 auto;
  }

  .welcome-features h2 {
    text-align: center;
  }

  .features-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }

  .welcome-cta {
    text-align: center;
  }

  .cta-text {
    text-align: center;
  }

  .cta-buttons {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .welcome-features {
    padding: 2rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

@media (max-width: 576px) {
  .welcome-features {
    padding: 1.5rem;
  }

  .welcome-content .welcome-buttons {
    flex-direction: column;
    gap: 1rem;
    width: 100%;
  }

  .welcome-content .welcome-buttons .btn {
    width: 100%;
    text-align: center;
  }
}

/* Button Styles */
.btn {
  display: inline-block;
  padding: 1rem 2.5rem;
  border-radius: 50px;
  font-weight: 600;
  font-size: 1.05rem;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.btn::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -2;
}

.btn::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0%;
  height: 100%;
  transition: all 0.3s;
  z-index: -1;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  color: white;
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(255, 48, 48, 0.3);
}

.btn-primary::after {
  background-color: var(--primary-color);
}

.btn-primary::before {
  background-color: #d01010;
}

.btn-primary:hover::before {
  width: 100%;
}

.btn-outline {
  background-color: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.btn-outline:hover {
  color: white;
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(255, 48, 48, 0.2);
}

.btn-outline::after {
  background-color: transparent;
}

.btn-outline::before {
  background-color: var(--primary-color);
}

.btn-outline:hover::before {
  width: 100%;
}

/* Plyr Video Player Customization */
.plyr {
  border-radius: 15px;
  overflow: hidden;
}

.plyr--video {
  background-color: var(--dark-bg);
}

.plyr__control--overlaid {
  background-color: var(--primary-color);
}

.plyr--full-ui input[type=range] {
  color: var(--primary-color);
}

.plyr__control.plyr__tab-focus,
.plyr__control:hover,
.plyr__control[aria-expanded=true] {
  background-color: var(--primary-color);
}

/* Video Section */
.video-section {
  padding: 5rem 2rem;
  background-color: var(--dark-secondary);
}

.section-title {
  font-size: 2.5rem;
  text-align: center;
  margin-bottom: 3rem;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background-color: var(--primary-color);
  border-radius: 2px;
}

.video-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 1rem;
}

.video-grid {
  display: flex;
  justify-content: space-between;
  width: 100%;
  flex-wrap: nowrap;
  gap: 1.5rem;
}

.video-item {
  flex: 1;
  min-width: 350px;
  max-width: calc(33.333% - 1rem);
  border-radius: 15px;
  overflow: hidden;
  background-color: var(--dark-bg);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.video-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

@media (max-width: 1200px) {
  .video-grid {
    flex-wrap: wrap;
    justify-content: center;
  }

  .video-item {
    flex: 0 0 calc(50% - 1rem);
    max-width: calc(50% - 1rem);
    margin-bottom: 2rem;
  }
}

@media (max-width: 768px) {
  .video-section {
    padding: 4rem 1.5rem;
  }

  .video-grid {
    flex-direction: column;
    align-items: center;
  }

  .video-item {
    flex: 0 0 100%;
    max-width: 550px;
    width: 100%;
    margin-bottom: 2rem;
  }
}

/* Product Cards */
.products-section {
  padding: 5rem 2rem;
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.product-card {
  background-color: var(--dark-secondary);
  border-radius: 15px;
  padding: 2rem;
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.product-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
}

.product-icon {
  font-size: 3rem;
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.product-title {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  font-family: 'Roboto', sans-serif;
  font-weight: 700;
}

.product-description {
  color: var(--text-gray);
  margin-bottom: 1.5rem;
  flex-grow: 1;
}

@media (max-width: 992px) {
  .products-section {
    padding: 4rem 2rem;
  }

  .product-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .products-section {
    padding: 4rem 1.5rem;
  }

  .product-grid {
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  }

  .product-card {
    padding: 1.5rem;
  }
}

@media (max-width: 576px) {
  .product-grid {
    grid-template-columns: 1fr;
    max-width: 350px;
    margin: 0 auto;
  }
}

/* Call to Action Section */
.cta-section {
  padding: 6rem 2rem;
  background-color: var(--dark-secondary);
  background-image: linear-gradient(to bottom, rgba(18, 18, 18, 0.7), rgba(30, 30, 30, 0.8)), url('../img/empty-studio-backdrop-seamless-.jpg');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
}

.cta-container {
  max-width: 900px;
  margin: 0 auto;
  text-align: center;
}

.cta-section .section-title {
  color: white;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

.cta-content {
  margin-top: 2rem;
  padding: 3rem;
  background-color: rgba(18, 18, 18, 0.75);
  border-radius: 15px;
  backdrop-filter: blur(8px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.cta-text {
  font-size: 1.3rem;
  line-height: 1.6;
  color: var(--text-light);
  margin-bottom: 2.5rem;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
}

.cta-subheadline {
  font-size: 1.6rem;
  color: var(--primary-color);
  margin: 1.5rem 0 2px 0;
  text-align: center;
  font-weight: 600;
}

.cta-buttons {
  display: flex;
  justify-content: center;
  gap: 2rem;
}

.cta-buttons-centered {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
}

.cta-buttons .btn,
.cta-buttons-centered .btn {
  padding: 1rem 2.5rem;
  font-size: 1.1rem;
  min-width: 200px;
}

.btn-large {
  padding: 1.2rem 3rem !important;
  font-size: 1.3rem !important;
  min-width: 250px !important;
}

.btn-extra-large {
  padding: 1.5rem 4rem !important;
  font-size: 1.5rem !important;
  min-width: 320px !important;
  font-weight: 700 !important;
}

@media (max-width: 768px) {
  .cta-section {
    padding: 4rem 1.5rem;
  }

  .cta-content {
    padding: 2rem;
  }

  .cta-text {
    font-size: 1.1rem;
  }

  .cta-buttons-centered {
    gap: 1.5rem;
  }

  .cta-buttons-centered .btn {
    min-width: 180px;
  }

  .btn-extra-large {
    padding: 1.3rem 3rem !important;
    font-size: 1.3rem !important;
    min-width: 280px !important;
  }
}

@media (max-width: 576px) {
  .cta-content {
    padding: 1.5rem;
  }

  .cta-buttons,
  .cta-buttons-centered {
    flex-direction: column;
    gap: 1rem;
    max-width: 250px;
    margin: 0 auto;
  }

  .cta-buttons .btn,
  .cta-buttons-centered .btn {
    width: 100%;
  }

  .btn-extra-large {
    padding: 1.2rem 2rem !important;
    font-size: 1.2rem !important;
    min-width: auto !important;
    width: 100% !important;
  }
}

/* Footer */
.footer {
  background-color: var(--dark-bg);
  padding: 3rem 2rem 0;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
}

.footer-logo img {
  height: 40px;
  margin-bottom: 1rem;
  filter: brightness(0) invert(1);
}

.footer-description {
  color: var(--text-gray);
  margin-bottom: 1.5rem;
}

.footer-title {
  font-size: 1.2rem;
  margin-bottom: 1.5rem;
  position: relative;
  font-family: 'Roboto', sans-serif;
  font-weight: 700;
}

.footer-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 40px;
  height: 3px;
  background-color: var(--primary-color);
  border-radius: 1.5px;
}

.footer-links {
  list-style: none;
}

.footer-links li {
  margin-bottom: 0.8rem;
}

.footer-links a {
  color: var(--text-gray);
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: var(--primary-color);
}

.social-links {
  display: flex;
  gap: 1rem;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #2a2a2a;
  color: var(--text-light);
  transition: all 0.3s ease;
}

.social-link:hover {
  background-color: var(--primary-color);
  transform: translateY(-3px);
}

.copyright {
  text-align: center;
  padding: 2rem 0;
  margin-top: 2rem;
  border-top: 1px solid #2a2a2a;
  color: var(--text-gray);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  position: relative;
}

.copyright-text {
  width: 100%;
}

.footer-legal {
  display: flex;
  justify-content: center;
  gap: 2rem;
  flex-wrap: wrap;
  margin-bottom: 0.5rem;
}

.footer-legal a {
  color: var(--text-gray);
  text-decoration: none;
  transition: color 0.3s ease;
  font-size: 0.9rem;
}

.footer-legal a:hover {
  color: var(--primary-color);
}

/* Admin Link */
.admin-link {
  margin-left: 1rem;
  color: var(--text-gray);
  text-decoration: none;
  font-size: 0.9rem;
  opacity: 0.7;
  transition: all 0.3s ease;
}

.admin-link:hover {
  color: var(--primary-color);
  opacity: 1;
}

/* Utility Classes */
.text-center {
  text-align: center;
}

.mt-12 {
  margin-top: 3rem;
}

.w-full {
  width: 100%;
}

.mr-2 {
  margin-right: 0.5rem;
}

/* Website coded with love text */
.coded-with-love {
  position: absolute;
  bottom: 15px;
  right: 20px;
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.5);
  text-align: right;
  transition: all 0.3s ease;
  opacity: 0.7;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.coded-with-love:hover {
  opacity: 1;
  transform: translateY(-3px);
  color: rgba(255, 255, 255, 0.9);
}

.coded-with-love .heart {
  color: var(--primary-color);
  animation: heartbeat 1.5s infinite ease-in-out;
  display: inline-block;
}

@keyframes heartbeat {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.2); }
}

/* Cookie Consent Banner */
.cookie-banner {
  position: fixed;
  bottom: 30px;
  right: 30px;
  max-width: 375px;
  background-color: rgba(30, 30, 30, 0.95);
  color: var(--text-light);
  padding: 1.5rem;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  z-index: 9999;
  display: none;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 0.95rem;
  line-height: 1.6;
  transform: translateY(100px);
  opacity: 0;
  transition: transform 0.5s ease, opacity 0.5s ease;
}

.cookie-banner.show {
  display: block;
  transform: translateY(0);
  opacity: 1;
}

.cookie-banner p {
  margin-bottom: 1.2rem;
  color: var(--text-gray);
}

.cookie-banner a {
  color: var(--primary-color);
  text-decoration: none;
  transition: opacity 0.3s ease;
}

.cookie-banner a:hover {
  text-decoration: underline;
  opacity: 0.9;
}

.cookie-buttons {
  display: flex;
  gap: 0.8rem;
  justify-content: flex-end;
}

.cookie-btn {
  padding: 0.6rem 1.2rem;
  border-radius: 5px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  border: none;
}

.cookie-btn-accept {
  background-color: var(--primary-color);
  color: white;
}

.cookie-btn-accept:hover {
  background-color: #e62b2b;
}

.cookie-btn-decline {
  background-color: transparent;
  color: var(--text-gray);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.cookie-btn-decline:hover {
  background-color: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.3);
}

@media (max-width: 576px) {
  .cookie-banner {
    left: 15px;
    right: 15px;
    bottom: 15px;
    max-width: none;
  }

  .cookie-buttons {
    flex-direction: column;
  }

  .cookie-btn {
    width: 100%;
    text-align: center;
  }
}
